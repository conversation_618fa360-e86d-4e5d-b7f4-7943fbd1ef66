using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

using RxjhServer.HelperTools;
using RxjhServer.Network;

using static System.Net.Mime.MediaTypeNames;
using HeroYulgang.Helpers;
using RxjhServer.Database;
using HeroYulgang.Core.Managers;
using RxjhServer.AOI;
using HeroYulgang.Utils;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Public;
using RxjhServer.Commands;
using HeroYulgang.Services;

namespace RxjhServer;

public partial class Players
{



	public bool ParseCommand(string Message)
	{
		var num = 0;
		try
		{
			var text = Message[0].ToString();
			num = 1;
			if (Message[0] != '!' && Message[0] != '@')
			{
				return false;
			}
			var array = Message.Split(' ');
			var text2 = array[0];
			num = 5;
			if (text2.Contains("!"))
			{
				text2 = text2.Replace("!", "!");
			}
			num = 6;
			if (text2.Contains("@"))
			{
				text2 = text2.Replace("@", "@");
				// logo.Log_Command_ingame_Lenh_an("[" + Userid + "] [" + UserName + "] : " + Message);
			}
			num = 7;


			if (array.Length >= 1 && (array[0] == "!thangchuc" || array[0] == "!tc"))
			{
				if (array.Length == 1)
				{
					_mission.THANG_CHUC_AUTO(this);
					return false;
				}
				int inputLevel = int.Parse(array[1]);
				if (inputLevel == 2)
				{
					HeThongNhacNho("Đại hiệp cần sử dụng lênh [!chinh] để gia nhập chính phái", 7, "Thiên cơ các");
					HeThongNhacNho("Đại hiệp cần sử dụng lênh [!ta] để gia nhập tà phái", 7, "Thiên cơ các");

				}
				else
				{
					if (inputLevel == 1)
					{
						if (base.Player_Job_level != 0 || base.Player_Level < 11)
						{
							HeThongNhacNho("Đại hiệp cần phải đạt cấp [10] mới có thể thăng chức lần 1!!!", 7, "Thiên cơ các");
							return false;
						}
						CharacterToProfession(0, 1);
						ThanNuVoCongDiemSo += 5;
					}
					else
					{
						if (base.Player_Job_level != inputLevel - 1 || base.Player_Level < _mission.GetLevelThangChuc(inputLevel))
						{
							HeThongNhacNho("Bạn phải có cấp độ từ [" + _mission.GetLevelThangChuc(inputLevel) + "] trở lên và đã thăng chức [" + (inputLevel - 1) + "] !!!", 7, "Thiên cơ các");
							return false;
						}
						CharacterToProfession(base.Player_Zx, inputLevel);
						ThanNuVoCongDiemSo += 5;
					}
					UpdateKinhNghiemVaTraiNghiem();
					UpdateMartialArtsAndStatus();
				}
			}
			if (array.Length >= 1 && array[0] == "!chinh")
			{
				if (base.Player_Level < 35)
				{
					HeThongNhacNho("Đại hiệp cần cấp độ [35] để gia nhập chính phái", 7, "Thiên cơ các");
					return false;
				}
				if (base.Player_Job_level >= 2 || base.Player_Zx != 0)
				{
					HeThongNhacNho("Bạn đã gia nhập thế lực, không thể thay đổi!!!", 7, "Thiên cơ các");
					return false;
				}

				CharacterToProfession(1, 2);
				HeThongNhacNho("Gia nhập [Chính phái] thành công!!!", 20, "Thiên cơ các");
				UpdateKinhNghiemVaTraiNghiem();
				UpdateMartialArtsAndStatus();
			}
			if (array.Length >= 1 && array[0] == "!ta")
			{
				if (base.Player_Level < 35)
				{
					HeThongNhacNho("Đại hiệp cần cấp độ [35] để gia nhập tà phái", 7, "Thiên cơ các");
					return false;
				}
				if (base.Player_Job_level >= 2 || base.Player_Zx != 0)
				{
					HeThongNhacNho("Bạn đã gia nhập thế lực, không thể thay đổi!!!", 7, "Thiên cơ các");
					return false;
				}
				CharacterToProfession(2, 2);
				HeThongNhacNho("Gia nhập [Tà phái] thành công!!!", 20, "Thiên cơ các");
				UpdateKinhNghiemVaTraiNghiem();
				UpdateMartialArtsAndStatus();
			}

			if (array[0] == World.Lenh_Buff_Cung)
			{
				// Buff_Cung_Team_KTTX();
			}
			else if (array[0] == World.Lenh_Buff_Bong)
			{
				if (MapID == 801 || true)
				{
					Buff_Bong();
				}
				else
				{
					HeThongNhacNho("Chỉ sử dụng trong bản đồ Thế Lực Chiến !!");
				}
			}
			else if (array.Length >= 1 && array[0] == "!batdau")
			{
				if (GMMode != 0)
				{
					if (World.eve != null || MapID == 801)
					{
						HeThongNhacNho("Không thể sử dụng vào thời điểm này !!");
						return true;
					}
					foreach (var value11 in World.allConnectedChars.Values)
					{
						if (FindPlayers(400, value11))
						{
							value11.HeThongNhacNho("CHUẨN BỊ !!!", 7, "Thiên cơ các");
							value11.Time_Dem_Nguoc(5);
						}
					}
					return true;
				}
			}
			else if (!(array[0] == World.Lenh_Buff_DaiPhu))
			{
				if (array.Length >= 1 && array[0] == World.Lenh_Buff_CPVP)
				{
					if (FLD_VIP == World.FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han)
					{
						NhanVat_SP = CharacterMax_SP;
						if (Player_Job == 6)
						{
							NoKhi_SoLuong = 5;
						}
						else if (Player_Job == 11)
						{
							NoKhi_SoLuong = 3;
						}
						CapNhat_HP_MP_SP();
					}
				}
				else if (array[0] == "@buffcamsu123" && Player_Job == 7)
				{
					Buff_CamSu();
				}
			}
			num = 12;
			if (array.Length >= 2 && array[0] == World.Add_Drop)
			{
				if (GMMode != 0)
				{
					var itemPid = int.Parse(array[1]);
					DROP_ITEM_ADD(itemPid, 0, 0, 0, 0, 0, "", this);
				}
				var text5 = AccountID + "-" + CharacterName + ":";
				var num11 = 0;
				while (num11 < array.Length)
				{
					text5 = text5 + " " + array[num11];
					var num6 = num11 + 1;
					num11 = num6;
				}
				// logo.Log_Add_Item_Member(text5, UserName);
			}
			if (array.Length >= 1 && array[0] == "!autolearn")
			{

				if (array[0] != null)
					AutoLearnSkill();
				UpdateMartialArtsAndStatus();
				return true;
			}

			if (array.Length >= 1 && array[0] == "!test")
			{
				// var itemId = 8;
				// if (array.Length >= 2)
				// {
				// 	int.TryParse(array[1], out itemId);
				// }
				// Init_Item_In_Bag();
				// var item = World.CreateAnItem(itemId, 1);
				// var description = $"Bạn đã nhận được phần thưởng: {item.GetItemName()} từ [GM]. Vui lòng đến [Bát Quái Lão Nhân] để nhận thưởng";

				// var byteDesc = System.Text.Encoding.GetEncoding(1252).GetBytes(Unitoccp1258(description));
				// World.SendGmMail("[GM]", this.AccountID, byteDesc, 0, item, 30);
				// SendMailCodNotificationByAdmin(SessionID);
				//SendMailCodNotification(this);
				//SendNewCodIdNotification(this,123);
				// Thông báo cho người chơi
				return true;
			}
			if (array.Length >= 1 && array[0] == "!re1")
			{
                SetPersonalMedicine();
				//World.conn.Transmit($"GROUP_QUEST_CANCEL|{UserName}|{this.GuildId}|{12000}");
				// HeThongNhacNho("Current Zone "+CurrentZone.ID);
				//HeThongNhacNho("Lich Trinh Boss ");
				//World.WorldBossEvent.CheckSchedule();
				AOIExtensions.UpdateAOIPosition(this, this.PosX, this.PosY);
				return true;
			}
			if (array.Length >= 1 && array[0] == "!re")
			{
				// ShowPlayers();
				if (!World.WToDoi.TryGetValue(TeamID, out var team)) 
					team.ThamGiaThanhVienNhom_NhacNho(this);
				// UpdateMoneyAndWeight();
				//SendMailCodNotificationByAdmin(this);
				// SendNewCodIdNotification(this, 123);
				//UpdateCharacterData(this);
				//UpdateBroadcastCharacterData();
				//CapNhat_HP_MP_SP();
				AutoLearnSkill();
				UpdateKinhNghiemVaTraiNghiem();
				UpdateMartialArtsAndStatus();
				//TipsAfterTheUpgrade(1);
				//SaveCharacterDataAsync().GetAwaiter().GetResult();
				// Init_Item_In_Bag();
				//UpdateMartialArtsAndStatus();
				// LoadCharacterWearItem();
				// ServerTime();
				//GetReviewScopeNpc();
				//SetPublicGoods();
				//num = 21;
				//SetThoiGianVatPham();
				//num = 22;
				//SetTitleItems(); // ok drug
				return true;
			}
			if (array.Length >= 1 && array[0] == "!item")
			{
				var slot = 0;
				if (array.Length >= 2)
					int.TryParse(array[1], out slot);
				var item = Item_In_Bag[slot];
				HeThongNhacNho($"Item {item.GetVatPham_ID} rs1 :{item.FLD_RESIDE1}  rs 2:{item.FLD_RESIDE2}", 7, "DEBUG");
			}

			if (array.Length >= 1 && array[0] == "!playlist")
			{
				foreach (var player in PlayerList.Values)
				{
					HeThongNhacNho($"Player : {player.SessionID} - {player.CharacterName}");
				}
			}
			num = 13;
			if (array[0] == "!delq")
			{
				DelAllTaskItem();
			}
			else if (array.Length >= 1 && array[0] == World.Kick_UserName_TLC)
			{
				TLC_AUTO_KICK_PLAYER(array[1]);
			}

			num = 14;
			if (array.Length >= 2 && array[0] == "!mokhoavatpham")
			{
				//var num17 = -1;
				//try
				//{
				//	num17 = int.Parse(array[1]);
				//}
				//catch
				//{
				//}
				//if (num17 >= 1 || num17 < 0)
				//{
				//	HeThongNhacNho("Chỉ có thể xóa ô đầu tiên [0]", 13, "Thiên cơ các");
				//}
				//else
				//{
				//	if (GMMode == 0 && FLD_RXPIONT != 100000)
				//	{
				//		HeThongNhacNho("Vật phẩm không phải là Thần Khí", 20, "Thiên cơ các");
				//		return true;
				//	}
				//	var array3 = new byte[56];
				//	Item_In_Bag[num17].VatPham_KhoaLai = false;
				//	Item_In_Bag[num17].Lock_Move = false;
				//	var value = (int)Item_In_Bag[num17].GetVatPham_ID;
				//	var itemGlobal_ID = Item_In_Bag[num17].ItemGlobal_ID;
				//	System.Buffer.BlockCopy(Item_In_Bag[num17].VatPham_byte, 16, array3, 0, World.VatPham_ThuocTinh_KichThuoc);
				//	GiamDi_VatPham(num17, 1);
				//	AddItems_Lock(itemGlobal_ID, BitConverter.GetBytes(value), num17, BitConverter.GetBytes(1), array3, khoaLai: false, 0);
				//}
			}
			else if (array.Length >= 2 && array[0] == "!doidanhhieu")
			{
				// var num18 = -1;
				// try
				// {
				// 	num18 = int.Parse(array[1]);
				// }
				// catch
				// {
				// }
				// if (num18 >= 1 || num18 < 0)
				// {
				// 	HeThongNhacNho("Chỉ có thể đổi vật phẩm ở ô đầu tiên [0]", 13, "Thiên cơ các");
				// }
				// else
				// {
				// 	var array4 = new byte[56];
				// 	Item_In_Bag[num18].VatPham_KhoaLai = false;
				// 	Item_In_Bag[num18].Lock_Move = false;
				// 	var num19 = (int)Item_In_Bag[num18].GetVatPham_ID;
				// 	var itemGlobal_ID2 = Item_In_Bag[num18].ItemGlobal_ID;
				// 	System.Buffer.BlockCopy(Item_In_Bag[num18].VatPham_byte, 16, array4, 0, World.VatPham_ThuocTinh_KichThuoc);
				// 	var num20 = 1000000661;
				// 	var num21 = 1000002051;
				// 	if (num19 == num20)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 1)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 1), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 2)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 2), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 3)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 3), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 4)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 4), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 5)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 5), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 6)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 6), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 7)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 7), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 8)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 8), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 9)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 9), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 10)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 10), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 11)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 11), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 12)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 12), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else if (num19 == num20 + 13)
				// 	{
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 13), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// 	else
				// 	{
				// 		if (num19 != num20 + 14)
				// 		{
				// 			HeThongNhacNho("Vật phẩm không phải là Danh Hiệu Hiệp Khách", 20, "Thiên cơ các");
				// 			return true;
				// 		}
				// 		GiamDi_VatPham(num18, 1);
				// 		AddItems_Lock(itemGlobal_ID2, BitConverter.GetBytes(num21 + 14), num18, BitConverter.GetBytes(1), array4, khoaLai: false, 0);
				// 	}
				// }
			}
			else if (array.Length >= 3 && array[0] == "!pos")
			{
				try
				{
					if (GMMode == 8)
					{
						var map = MapID;
						if (array.Length == 4)
						{
							map = int.Parse(array[3]);

						}
						Mobile(float.Parse(array[1]), float.Parse(array[2]), 15, map, 1);
					}
				}
				catch (Exception)
				{

					HeThongNhacNho("Lỗi chuyển động");
				}

				//HeThongNhacNho("[" + NhanVatToaDo_BanDo + "][" + NhanVatToaDo_X + "][" + NhanVatToaDo_Y + "]");
			}
			else
			{
				if (array.Length >= 2 && text2 == "!move")
				{
					if (GMMode == 0 && MapID == 801 && World.TheLucChien_Progress <= 3 && World.tmc_flag)
					{
						HeThongNhacNho("Thế Lực Chiến đang diễn ra, không thể di chuyển.");
						return true;
					}
					if (GMMode == 0 && MapID == 40101 && World.DCH_Progress < 6)
					{
						HeThongNhacNho("ĐẠI CHIẾN HỒN đang diễn ra, không thể di chuyển");
						return true;
					}
					if (!OpenWarehouse && MapID != 2301 && MapID != 2341 && MapID != 9001 && MapID != 9101 && MapID != 9201 && NhanVat_HP != 0)
					{
						if (World.WhetherTheCurrentLineIsSilver == 1)
						{
							HeThongNhacNho("Mời đến quảng trường tiền tệ, chỗ di động về huyền bột phái.");
							return true;
						}
						if (array[1] != "银币广场" && !OpenWarehouse && MapID != 2501)
						{
							if (Player_Money < 100)
							{
								HeThongNhacNho("Đến 100 lượng còn không có thì đòi đi đâu ???");
								return true;
							}
							var flag = false;
							foreach (var item in World.DiDong)
							{
								if (!(item.Rxjh_name == array[1]))
								{
									continue;
								}
								var array5 = World.BanDoKhoa_Chat.Split(';');
								if (array5.Length >= 1)
								{
									for (var l = 0; l < array5.Length; l++)
									{
										if (int.Parse(array5[l]) == item.Rxjh_Map)
										{
											return true;
										}
									}
								}
								if (GMMode != 0 || (GMMode == 0 && (item.Rxjh_Map == World.HuyenBotPhai || item.Rxjh_Map == World.TamTaQuan || item.Rxjh_Map == World.LieuChinhQuan || item.Rxjh_Map == World.LieuThienPhu || item.Rxjh_Map == World.NamMinhHieu || item.Rxjh_Map == World.ThanVoMon || item.Rxjh_Map == World.TungNguyetQuan || item.Rxjh_Map == World.BachVoQuan || item.Rxjh_Map == World.BacHaiBangCung || item.Rxjh_Map == World.NamLam || item.Rxjh_Map == World.HoHapCoc || item.Rxjh_Map == World.XichThienGioi || item.Rxjh_Map == World.ThienDuSon || item.Rxjh_Map == World.ThanhDiaKiemHoang || item.Rxjh_Map == World.PhongThanKhau)))
								{
									if (item.Rxjh_Map == 9001)
									{
										if (World.IsHuaMarriageHallInUse)
										{
											if (World.Weddinglist.TryGetValue(9001, out var value3))
											{
												if (value3.Wedding_Progress == 0)
												{
													Mobile(item.Rxjh_X, item.Rxjh_Y, item.Rxjh_Z, item.Rxjh_Map, 0);
													flag = true;
													break;
												}
												HeThongNhacNho("Lễ cưới đã bắt đầu hoặc đã kết thúc.");
												return true;
											}
											HeThongNhacNho("Này tiệc cưới sảnh còn không có Wedding Cử hành.");
											return true;
										}
										HeThongNhacNho("Này tiệc cưới sảnh không có Wedding Cử hành.");
										return true;
									}
									if (item.Rxjh_Map == 9101)
									{
										if (World.IsTheDragonHallInUse)
										{
											if (World.Weddinglist.TryGetValue(9101, out var value4))
											{
												if (value4.Wedding_Progress == 0)
												{
													Mobile(item.Rxjh_X, item.Rxjh_Y, item.Rxjh_Z, item.Rxjh_Map, 0);
													flag = true;
													break;
												}
												HeThongNhacNho("Lễ cưới đã bắt đầu hoặc đã kết thúc.");
												return true;
											}
											HeThongNhacNho("Này tiệc cưới sảnh còn không có Wedding Cử hành.");
											return true;
										}
										HeThongNhacNho("Này tiệc cưới sảnh không có Wedding Cử hành.");
										return true;
									}
									if (item.Rxjh_Map != 9201)
									{
										Mobile(item.Rxjh_X, item.Rxjh_Y, item.Rxjh_Z, item.Rxjh_Map, 0);
										if (item.Rxjh_Map >= World.KhuLuyenTap1 && item.Rxjh_Map <= World.KhuLuyenTap9 && World.ServerChoPKHayKhong != 0)
										{
											SwitchPkMode(1);
										}
										flag = true;
										break;
									}
									if (World.WhetherTheSacramentalHallIsInUse)
									{
										if (World.Weddinglist.TryGetValue(9201, out var value5))
										{
											if (value5.Wedding_Progress == 0)
											{
												Mobile(item.Rxjh_X, item.Rxjh_Y, item.Rxjh_Z, item.Rxjh_Map, 0);
												flag = true;
												break;
											}
											HeThongNhacNho("Lễ cưới đã bắt đầu hoặc đã kết thúc.");
											return true;
										}
										HeThongNhacNho("Này tiệc cưới sảnh còn không có Wedding Cử hành.");
										return true;
									}
									HeThongNhacNho("Này tiệc cưới sảnh không có Wedding Cử hành.");
									return true;
								}
								HeThongNhacNho("Bản đồ đang khóa !!", 20, "Thiên cơ các");
								return true;
							}
							if (!flag)
							{
								HeThongNhacNho("Tên bản đồ không tồn tại.");
							}
							else
							{
								Player_Money -= 100L;
								UpdateMoneyAndWeight();
							}
							return true;
						}
						HeThongNhacNho("Quay trở về Huyền Bột Phái !!");
						Mobile(420f, 1740f, 15f, 101, 0);
						return true;
					}
					return true;
				}
				//if (array.Length >= 1 && array[0] == "!pointthannu" && GMMode != 0)
				//{
				//	ThanNuVoCongDiemSo = 50;
				//	UpdateMartialArtsAndStatus();
				//}
			}
			num = 15;
			if (array.Length >= 1 && array[0] == "!trace")
			{
				if (GMMode != 0)
				{
					var playerByName = GetPlayerByName(array[1]);
					if (playerByName != null)
					{
						Mobile(playerByName.PosX, playerByName.PosY, 15f, playerByName.MapID, 0);
					}
					else
					{
						HeThongNhacNho("Đối phương không online.", 10, "Thiên cơ các");
					}
					return true;
				}
			}
			else if (array.Length >= 1 && array[0] == World.CTC)
			{
				ParticipateInASiege();
			}
			else if (array.Length >= 2 && array[0] == World.Lenh_Party_1)
			{
				Regex regex = new("[a-zA-Z0-9]");
				if (!regex.Match(array[1]).Success)
				{
					HeThongNhacNho("Tên nhân vật không hợp lệ", 20, "Thiên cơ các");
					return true;
				}
				if (DateTime.Now.Subtract(time_party).TotalSeconds < 10.0)
				{
					HeThongNhacNho("Giới hạn sử dụng [10]s, còn lại [" + (10 - (int)DateTime.Now.Subtract(time_party).TotalSeconds) + "]s", 20, "Thiên cơ các");
				}
				else
				{
					SendTeam(array[1]);
				}
			}
			else if (array.Length >= 1 && array[0] == "!lyhon")
			{
				LyHon_TuBo_KetHon();
			}
			//else if (array.Length >= 1 && array[0] == World.Lenh_Nhiem_Vu)
			//{
			//	var num23 = 0;
			//	var player_Money = Player_Money;
			//	num23 = ((Player_WuXun < 1200000) ? ********* : 1000000000);
			//	if (Player_Money < num23)
			//	{
			//		if (Player_WuXun >= 1200000)
			//		{
			//			HeThongNhacNho("Võ Huân trên 1,200,000  1,000,000,000 lượng để hoàn thành nhiệm vụ", 20, "Thiên cơ các");
			//			return true;
			//		}
			//		HeThongNhacNho("Võ Huân dưới 1,200,000 Bạn cần 500,000,000 lượng để hoàn thành nhiệm vụ", 20, "Thiên cơ các");
			//		return true;
			//	}
			//	if (num23 > 0)
			//	{
			//		Player_Money -= num23;
			//		UpdateMoneyAndWeight();
			//		SetUpQuestItems(World.Item_Quest, 100);
			//		HeThongNhacNho("Bạn bị trừ 500,000,000 lượng", 10, "Thiên cơ các");
			//	}
			//	else
			//	{
			//		HeThongNhacNho("Ngân lượng về 0, không thể trả nhiệm vụ !!!", 10, "Thiên cơ các");
			//	}
			//	var txt = "[" + Userid + "] [" + UserName + "] Truoc:[" + player_Money + "] Sau:[" + Player_Money + "]";
			//	// logo.Log_Lenh_Add_Quest(txt);
			//}
			else if (array.Length >= 0 && text2 == "!pvpdragon")
			{
				if (GMMode != 0)
				{
					var a = int.Parse(array[1]);
					var b = int.Parse(array[2]);
					var c = int.Parse(array[3]);
					PvpDragonBattle(a, b, c);
				}
			}
			else if (array.Length >= 1 && text2 == "!deadplayer")
			{
				if (GMMode != 0)
				{
					if (PlayerList != null && PlayerList.TryGetValue((World.ServerID, SessionID), out var value6) && FindPlayers(1000, value6) && value6.GMMode == 0)
					{
						value6.PlayerTuVong = true;
						value6.Death();
					}
					return true;
				}
			}
			else if (array.Length >= 1 && array[0] == "!deadnpc")
			{
				if (GMMode != 0)
				{
					try
					{
						foreach (var value12 in NpcList.Values)
						{
							value12.GuiDiTuVongSoLieuWrapper(SessionID);
						}
					}
					catch
					{
					}
					return true;
				}
			}
			else if (array.Length >= 2 && array[0] == "!delnpc")
			{
				if (GMMode != 0)
				{
					var num24 = -1;
					try
					{
						num24 = int.Parse(array[1]);
						World.delNpc(num24, MapID);
					}
					catch
					{
					}
					return true;
				}
			}
			else if (array.Length >= 2 && text2 == "!thongbao")
			{
				try
				{
					if (GMMode != 0)
					{
						foreach (var value13 in World.allConnectedChars.Values)
						{
							value13.HeThongNhacNho(array[1], 10, CharacterName);
						}
					}
				}
				catch
				{
				}
			}
			else if (array.Length >= 2 && text2 == "!kick")
			{
				if (GMMode != 0)
				{
					var playerByName2 = GetPlayerByName(array[1]);
					if (playerByName2.Client != null)
					{
						World.ToanCucNhacNho("", 7, "KICK NHÂN VẬT [" + playerByName2.CharacterName + "] KHỎI KÊNH !!");
						HeThongNhacNho("Kick ID:[" + playerByName2.AccountID + "] [" + playerByName2.CharacterName + "]", 10, "Thiên cơ các");
						playerByName2.BanAccount(888, playerByName2.AccountID, "Admin Kick");
						playerByName2.Client.Dispose();
					}
				}
			}
			//else if (array.Length >= 2 && Buffer.IsEquals(text2, "!trucxuat"))
			//{
			//	if (GangCharacterLevel == 6)
			//	{
			//		var text6 = array[1].Trim();
			//		var dataTable = RxjhClass.DatDuocBangPhaiSoLieu(GangName);
			//		if (dataTable != null)
			//		{
			//			if (NhanVatToaDo_BanDo == 42001)
			//			{
			//				HeThongNhacNho("Trước mắt bản đồ không thể rời khỏi bang.");
			//			}
			//			else if (dataTable.Rows.Count > 0)
			//			{
			//				var text7 = dataTable.Rows[0]["G_Master"].ToString();
			//				if (text6 == text7.Trim())
			//				{
			//					return false;
			//				}
			//				if (RxjhClass.TrucXuatBangPhai(text6, GangName) == 0)
			//				{
			//					var array6 = Converter.HexStringToByte("AA5530000A00E800220000000000000000000000000000000000000421617300000000000000000000000000000000000000000055AA");
			//					var bytes = Encoding.Default.GetBytes(text6);
			//					System.Buffer.BlockCopy(bytes, 0, array6, 29, bytes.Length);
			//					SendGangMessage(GangName, array6, array6.Length);
			//					var characterData = GetCharacterData(text6);
			//					if (characterData != null)
			//					{
			//						characterData.ReadGangData();
			//						characterData.KhoiTaoChungToiDa_TrangBiVatPham();
			//						characterData.Move_DiChuyen_RaKhoi_BanDo(NhanVatToaDo_BanDo);
			//						characterData.GetTheReviewRangePlayers();
			//					}
			//				}
			//			}
			//			dataTable.Dispose();
			//		}
			//	}
			//	else
			//	{
			//		HeThongNhacNho("Ngươi không phải bang chủ, không thể kích thành viên trong bang.");
			//	}
			//}
			else if (array.Length >= 1 && array[0] == World.TanHinh)
			{
				if (GMMode != 0)
				{
					if (HinhThuc_TangHinh == 0)
					{
						HinhThuc_TangHinh = 1;
						CheDo_TangHinh(1);
						HeThongNhacNho("Bạn đã trở nên tàng hình !!", 7, "Thiên cơ các");
					}
					else
					{
						HinhThuc_TangHinh = 0;
						CheDo_TangHinh(0);
						HeThongNhacNho("Xóa hiệu ứng tàng hình !!", 10, "Thiên cơ các");
					}
				}
			}
			else if (array.Length >= 2 && text2 == "!warguild")
			{
				if (GMMode != 0)
				{
					if (World.BangChien == null)
					{
						World.CoMoRa_HeThong_MonChien = 1;
						World.BangChien = new();
						HeThongNhacNho("Guild War bắt đầu");
					}
					else
					{
						World.BangChien.Dispose();
						HeThongNhacNho("Guild War kết thúc");
					}
					return true;
				}
			}
			else if (array.Length >= 1 && array[0] == "!del")
			{
				for (var num30 = 0; num30 < 36; num30++)
				{
					if (Item_In_Bag[num30].GetVatPham_ID != 0L && Item_In_Bag[num30].GetVatPham_ID != 1600001 && World.Check_VuKhi_Hero(Item_In_Bag[num30].GetVatPham_ID) == 0)
					{
						try
						{
							var text13 = "[" + AccountID + "] [!xoatuichinh ] Item:[" + Item_In_Bag[num30].GetVatPham_ID + "] CuongHoaSoLuong:[" + Item_In_Bag[num30].FLD_CuongHoaSoLuong + "] D0:[" + Item_In_Bag[num30].FLD_MAGIC0 + "] D1:[" + Item_In_Bag[num30].FLD_MAGIC1 + "] D2:[" + Item_In_Bag[num30].FLD_MAGIC2 + "] D3:[" + Item_In_Bag[num30].FLD_MAGIC3 + "] D4:[" + Item_In_Bag[num30].FLD_MAGIC4 + "] ThuocTinhSoLuong:[" + Item_In_Bag[num30].FLDThuocTinhSoLuong + "] ThuocTinhLoai:[" + Item_In_Bag[num30].FLDThuocTinhLoaiHinh + "] TuLinh:[" + Item_In_Bag[num30].FLD_TuLinh + "] SoulHa:[" + Item_In_Bag[num30].FLD_FJ_LowSoul + "] SoulTrung:[" + Item_In_Bag[num30].FLD_FJ_TrungCapPhuHon + "] TienHoa:[" + Item_In_Bag[num30].FLD_FJ_TienHoa + "] NgaySuDung:[" + Item_In_Bag[num30].FLD_FJ_NJ + "] Lock:[" + Item_In_Bag[num30].VatPham_KhoaLai + "]";
							// logo.Lenh_Xoa_Item(text13, UserName);
							Item_In_Bag[num30].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
						}
						catch
						{
							HeThongNhacNho("Vật phẩm tại ô số [" + num30 + "] bị lỗi, cần dùng lệnh !xoatatca 0 (ô đầu tiên để xóa nó)");
						}
					}
				}
				Init_Item_In_Bag();
				UpdateMoneyAndWeight();
				HeThongNhacNho("Bạn đã xóa các vật phẩm trên túi đồ chính !!", 10, "");
			}

			else if (array.Length >= 1 && array[0] == "!xoatuichinh")
			{
				for (var num30 = 0; num30 < 36; num30++)
				{
					if (!Item_In_Bag[num30].VatPham_KhoaLai && Item_In_Bag[num30].GetVatPham_ID != 0L && Item_In_Bag[num30].GetVatPham_ID != 1600001 && World.Check_VuKhi_Hero(Item_In_Bag[num30].GetVatPham_ID) == 0)
					{
						try
						{
							var text13 = "[" + AccountID + "] [!xoatuichinh ] Item:[" + Item_In_Bag[num30].GetVatPham_ID + "] CuongHoaSoLuong:[" + Item_In_Bag[num30].FLD_CuongHoaSoLuong + "] D0:[" + Item_In_Bag[num30].FLD_MAGIC0 + "] D1:[" + Item_In_Bag[num30].FLD_MAGIC1 + "] D2:[" + Item_In_Bag[num30].FLD_MAGIC2 + "] D3:[" + Item_In_Bag[num30].FLD_MAGIC3 + "] D4:[" + Item_In_Bag[num30].FLD_MAGIC4 + "] ThuocTinhSoLuong:[" + Item_In_Bag[num30].FLDThuocTinhSoLuong + "] ThuocTinhLoai:[" + Item_In_Bag[num30].FLDThuocTinhLoaiHinh + "] TuLinh:[" + Item_In_Bag[num30].FLD_TuLinh + "] SoulHa:[" + Item_In_Bag[num30].FLD_FJ_LowSoul + "] SoulTrung:[" + Item_In_Bag[num30].FLD_FJ_TrungCapPhuHon + "] TienHoa:[" + Item_In_Bag[num30].FLD_FJ_TienHoa + "] NgaySuDung:[" + Item_In_Bag[num30].FLD_FJ_NJ + "] Lock:[" + Item_In_Bag[num30].VatPham_KhoaLai + "]";
							// logo.Lenh_Xoa_Item(text13, UserName);
							Item_In_Bag[num30].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
						}
						catch
						{
							HeThongNhacNho("Vật phẩm tại ô số [" + num30 + "] bị lỗi, cần dùng lệnh !xoatatca 0 (ô đầu tiên để xóa nó)");
						}
					}
				}
				Init_Item_In_Bag();
				UpdateMoneyAndWeight();
				HeThongNhacNho("Bạn đã xóa các vật phẩm trên túi đồ chính !!", 10, "");
			}
			else if (array.Length >= 1 && array[0] == "!xoatuiphu")
			{
				for (var num31 = 36; num31 < 96; num31++)
				{
					if (!Item_In_Bag[num31].VatPham_KhoaLai && Item_In_Bag[num31].GetVatPham_ID != 0L && Item_In_Bag[num31].GetVatPham_ID != 1600001 && World.Check_VuKhi_Hero(Item_In_Bag[num31].GetVatPham_ID) == 0)
					{
						try
						{
							var text14 = "[" + AccountID + "] [!xoatuiphu ] Item:[" + Item_In_Bag[num31].GetVatPham_ID + "] CuongHoaSoLuong:[" + Item_In_Bag[num31].FLD_CuongHoaSoLuong + "] D0:[" + Item_In_Bag[num31].FLD_MAGIC0 + "] D1:[" + Item_In_Bag[num31].FLD_MAGIC1 + "] D2:[" + Item_In_Bag[num31].FLD_MAGIC2 + "] D3:[" + Item_In_Bag[num31].FLD_MAGIC3 + "] D4:[" + Item_In_Bag[num31].FLD_MAGIC4 + "] ThuocTinhSoLuong:[" + Item_In_Bag[num31].FLDThuocTinhSoLuong + "] ThuocTinhLoai:[" + Item_In_Bag[num31].FLDThuocTinhLoaiHinh + "] TuLinh:[" + Item_In_Bag[num31].FLD_TuLinh + "] SoulHa:[" + Item_In_Bag[num31].FLD_FJ_LowSoul + "] SoulTrung:[" + Item_In_Bag[num31].FLD_FJ_TrungCapPhuHon + "] TienHoa:[" + Item_In_Bag[num31].FLD_FJ_TienHoa + "] NgaySuDung:[" + Item_In_Bag[num31].FLD_FJ_NJ + "] Lock:[" + Item_In_Bag[num31].VatPham_KhoaLai + "]";
							// logo.Lenh_Xoa_Item(text14, UserName);
							Item_In_Bag[num31].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
						}
						catch
						{
							HeThongNhacNho("Vật phẩm tại ô số [" + num31 + "] bị lỗi, cần dùng lệnh !xoatatca 0 (ô đầu tiên để xóa nó)");
						}
					}
				}
				Init_Item_In_Bag();
				UpdateMoneyAndWeight();
				HeThongNhacNho("Bạn đã xóa các vật phẩm trên túi đồ phụ !!", 10, "");
			}
			else if (array.Length >= 1 && array[0] == World.Relog)
			{
				QuayLaiChonNhanVat(null, 0);
			}


			num = 20;
			if (array.Length >= 1 && array[0] == World.TreoShopOffine)
			{
				HeThongNhacNho("Vui lòng sử dụng Thiên cơ các chợ tại Npc Ân Quân");

			}



			if (array.Length >= 1 && array[0] == "!exp")
			{
				ThongTin_Bonus_Exp_Theo_ThangChuc();
			}

			// NPC Management Commands
			if (array.Length >= 1 && array[0].StartsWith("!npc"))
			{
				try
				{
					return HandleNpcCommand(Message, array);
				}
				catch (Exception ex)
				{
					HeThongNhacNho($"Lỗi NPC command: {ex.Message}", 20, "System");
					LogHelper.WriteLine(LogLevel.Error, $"NPC command error: {ex.Message}");
				}
				return true;
			}


			else if (array[0] == "!autopt")
			{
				Player_AutoPartyString = array[1];
				HeThongNhacNho("Tự động party [" + array[1] + "]", 7, "Thiên cơ các");
			}
			else if (array.Length >= 1 && array[0] == "!tatautopt")
			{
				Player_AutoPartyString = "";
				HeThongNhacNho("Tắt tự động party", 7, "Thiên cơ các");
			}
			num = 21;
			if (array.Length >= 1 && array[0] == World.Offline_DanhQuai)
			{
				if (!PublicDrugs.ContainsKey(World.Offline_Pill_ID))
				{
                    Logger.Instance.Info("Offline pill id " + World.Offline_Pill_ID);
					HeThongNhacNho("Đại hiệp cần sử dụng Chí Tôn Phù để sử dụng chức năng này !!", 20, "Thiên cơ các");
					return true;
				}
				if (VoCong_DanhLanCuoi_Khi_OffLine == 501203 || VoCong_DanhLanCuoi_Khi_OffLine == 501501 || VoCong_DanhLanCuoi_Khi_OffLine == 501502 || VoCong_DanhLanCuoi_Khi_OffLine == 501601 || VoCong_DanhLanCuoi_Khi_OffLine == 501602 || VoCong_DanhLanCuoi_Khi_OffLine == 501603)
				{
					offline_buff = true;
				}
				if (AppendStatusList.ContainsKey(700904))
				{
					HeThongNhacNho("Trạng thái bất tử không thể sử dụng lệnh này !!", 20, "Thiên cơ các");
					return true;
				}
				if (OpenWarehouse)
				{
					HeThongNhacNho("Đại hiệp không thể Offattack khi đang mở kho đồ !!", 20, "Thiên cơ các");
					return true;
				}
				if (CuaHangCaNhan != null && InTheShop)
				{
					HeThongNhacNho("Đại hiệp không thể Offattack khi đang mở bán hàng !!", 20, "Thiên cơ các");
					return true;
				}
				if (InTheShop)
				{
					HeThongNhacNho("Đại hiệp không thể Offattack khi đang mở cửa hàng !!", 20, "Thiên cơ các");
					return true;
				}
				SaveCharacterData();
				Client.OffAttack();
				return true;
			}
			if (array.Length >= 1 && array[0] == World.Offline_BuffMau_DP)
			{
				if (!TitleDrug.ContainsKey(World.Offline_Pill_ID))
				{
					HeThongNhacNho("Đại hiệp cần sử dụng Chí Tôn Phù để sử dụng chức năng này !!", 30, "Thiên cơ các");
					return true;
				}
				if (Player_Job != 5)
				{
					HeThongNhacNho("Chỉ có Đại Phu mới có thể sử dụng lệnh này", 30, "Thiên cơ các");
					return true;
				}
				if (AppendStatusList.ContainsKey(700904))
				{
					HeThongNhacNho("Trạng thái bất tử không thể sử dụng lệnh này !!", 20, "Thiên cơ các");
					return true;
				}
				if (OpenWarehouse)
				{
					HeThongNhacNho("Đại hiệp không thể Offattack khi đang mở kho đồ !!", 20, "Thiên cơ các");
					return true;
				}
				if (CuaHangCaNhan != null && InTheShop)
				{
					HeThongNhacNho("Đại hiệp không thể Offattack khi đang mở bán hàng !!", 20, "Thiên cơ các");
					return true;
				}
				if (InTheShop)
				{
					HeThongNhacNho("Đại hiệp không thể Offattack khi đang mở cửa hàng !!", 20, "Thiên cơ các");
					return true;
				}
				Auto_Train_Track = DateTime.Now.AddMilliseconds(-12000.0);
				Auto_TreoMay_TheoDoi_ThoiGian = 0;
				TuDongTiepTe = 1;
				ChucNang_Auto_ThucHien = 1;
				offline_buff = true;
				Offline_TreoMay_Mode_ON_OFF = 1;
				Offline_TreoMay_ToaDo_X = (int)PosX;
				Offline_TreoMay_ToaDo_Y = (int)PosY;
				Offline_TreoMay_BanDo = MapID;
				OfflineTreoMaySkill_ID = VoCong_DanhLanCuoi_Khi_OffLine;
				SaveCharacterData();
				// Auto_Offline_Timer = new(2000.0);
				// Auto_Offline_Timer.Elapsed += Auto_Offline_Function;
				// Auto_Offline_Timer.AutoReset = true;
				// Auto_Offline_Timer.Enabled = true;
				Client.OffAttack();
				return true;
			}
			num = 22;
			if (array.Length >= 1 && array[0] == World.Info_Thong_Tin)
			{
				//Check_Info_Player(UserName);
			}
			//if (array.Length >= 2 && (array[0] == World.Check_Info_Char || array[0] == "!info"))
			//{
			//	Check_Info_Player(array[1]);
			//}
			else if (array.Length >= 1 && array[0] == "!changech")
			{
				if (World.WhetherTheCurrentLineIsSilver == 1)
				{
					HeThongNhacNho("Mời đến quảng trường tiền tệ, để di chuyển về huyền bột phái.", 10, "Thiên cơ các");
					return true;
				}
				if (PlayerTuVong || Exiting || (CuaHangCaNhan != null && CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa) || (GiaoDich != null && GiaoDich.NguoiGiaoDich != null))
				{
					HeThongNhacNho("Hiện không thể di chuyển.", 10, "Thiên cơ các");
					return true;
				}
				var inputChannel = int.Parse(array[1]);
				if (inputChannel == OriginalServerSerialNumber)
				{
					HeThongNhacNho("Bạn đang ở [Kênh " + World.ServerID + "] rồi", 10, "Thiên cơ các");
					return true;
				}
				if (World.ServerList.TryGetValue(inputChannel, out var value9) && value9 != null)
				{
					HeThongNhacNho("Đang chuyển kênh, xin chờ giây lát...", 10, "Thiên cơ các");
					World.conn.Transmit("UPDATE_ORIGIN_SERVER | " + AccountID + " | " + inputChannel + " | " + value9.ServerIP + "|" + value9.ServerPort + "|" + value9.ServerID);
					ChangeLine(value9.ServerID, value9.ServerIP, value9.ServerPort);
					HeThongNhacNho("Kênh: " + value9.ServerID, 10, "Thiên cơ các");
					HeThongNhacNho("IP: " + value9.ServerIP, 10, "Thiên cơ các");
					HeThongNhacNho("Port: " + value9.ServerPort, 10, "Thiên cơ các");
				}
				else
				{
					HeThongNhacNho("Kênh chưa mở hoặc đang bảo trì.", 10, "Thiên cơ các");
				}
			}
			num = 23;

			num = 24;

			{
				if (array.Length >= 2 && array[0] == "!level")
				{
					if (GMMode != 0)
					{
						var num113 = -1;
						try
						{
							num113 = int.Parse(array[1]);
						}
						catch
						{
						}
						Player_Level += num113;
						UpdateMartialArtsAndStatus();
						TipsAfterTheUpgrade(1);
						UpdateKinhNghiemVaTraiNghiem();
					}
					return true;
				}
				if (array.Length >= 2 && array[0] == "!checkpointguild")
				{
					if (GMMode != 0)
					{
						var source = from x in World.GuildList.Values.ToList()
									 where x.PlayerName.ToLower() == array[1].ToLower()
									 select x;
						if (source.Any())
						{
							var num114 = source.Select(x => x.Guild_Score).FirstOrDefault();
							var myguildname2 = source.Select(x => x.GuildName).FirstOrDefault();
							var num115 = (from x in World.GuildList.Values.ToList()
										  where x.GuildName == myguildname2
										  select x).Sum(x => x.Guild_Score);
							HeThongNhacNho("[" + array[1] + "] => [" + num114 + "/" + num115 + "].");
						}
					}
					else
					{
						var source2 = from x in World.GuildList.Values.ToList()
									  where x.PlayerName.ToLower() == array[1].ToLower()
									  select x;
						var source3 = from x in World.GuildList.Values.ToList()
									  where x.PlayerName.ToLower() == CharacterName.ToLower()
									  select x;
						if (source2.Any() && source3.Any())
						{
							if (GangCharacterLevel > 4)
							{
								if (source2.FirstOrDefault().GuildName == source3.FirstOrDefault().GuildName)
								{
									var num116 = source2.Select(x => x.Guild_Score).FirstOrDefault();
									var myguildname = source2.Select(x => x.GuildName).FirstOrDefault();
									var num117 = (from x in World.GuildList.Values.ToList()
												  where x.GuildName == myguildname
												  select x).Sum(x => x.Guild_Score);
									HeThongNhacNho("[" + array[1] + "] => [" + num116 + "/" + num117 + "].");
								}
								else
								{
									HeThongNhacNho("Không cùng guild check cái quần què");
								}
							}
							else
							{
								HeThongNhacNho("Chỉ Bang Chủ và Phó Bang Chủ mới có thể sử dụng lệnh này !!");
							}
						}
					}
				}
			}

			if (array.Length >= 1 && array[0] == "!addgold")
			{
				if (GMMode != 8)
				{
					return false;
				}
				if (array.Length >= 2)
				{
					var num118 = int.Parse(array[1]);
					KiemSoatGold_SoLuong(num118, 1);
					UpdateMoneyAndWeight();
				}
			}

			if (array.Length >= 1 && array[0] == "!tester")
				{
					//if (Client.ToString() != "127.0.0.1")
					//{
					//	return true;
					//}
					if (GMMode == 0)
					{
						if (array.Length >= 2)
						{
							CharacterExperience = (long)World.lever[170 - 1];
							SkillExperience += 99999999;
							Player_WuXun = 5000000;
							KiemSoatGold_SoLuong(1000000000, 1);
							KiemSoatNguyenBao_SoLuong(50000, 1);
							Player_Level = 170;
							if (array[1] == "chinh")
							{
								CharacterToProfession(1, 12);
								ThanNuVoCongDiemSo += (5 * 12);
							}
							if (array[1] == "ta")
							{
								CharacterToProfession(2, 12);
								ThanNuVoCongDiemSo += (5 * 12);
							}
							TipsAfterTheUpgrade(1);
							TinhToan_NhanVatCoBan_DuLieu();
							UpdateKinhNghiemVaTraiNghiem();
							UpdateMartialArtsAndStatus();
							CapNhat_HP_MP_SP();
							SaveGangData();
							UpdateMoneyAndWeight();
						}
						HeThongNhacNho("Cấp Quyền Tester thành công!!!");
					}
					else
					{
						GMMode = 0;
						if (array.Length >= 2 && array[1] == "vinhvien")
						{
							// DBA.ExeSqlCommand($"UPDATE TBL_XWWL_Char SET FLD_J9=0 WHERE FLD_NAME='{UserName}'");
						}
						HeThongNhacNho("Hủy Quyền Tester thành công!!!");
					}
				}
			num = 25;
			if (array.Length >= 1 && array[0] == World.TrungSinh1)
			{
				TrungSinh_Level_165();
			}

			if (array.Length >= 1 && array[0] == "!despawnnpc")
			{

				Dictionary<int, NpcClass> dict = new();
				foreach (var npcEntry in NpcList.Values)
				{
					AOIManager.Instance.RemoveNPC(npcEntry.NPC_SessionID);
					//npcEntry.Dispose();
				}
				return true;
			}
			if (array.Length >= 2 && array[0] == "!alimit")
			{
				if (GMMode != 8)
				{
					return false;
				}

				if (array.Length == 1)
				{
					HeThongNhacNho($"Attack Limiter Enabled: {World.AttackLimiter_Enabled}", 7, "Attack Limiter");
					return true;
				}

				if (array[1] == "on")
				{
					World.AttackLimiter_Enabled = true;
					HeThongNhacNho("Attack Limiter Enabled", 7, "Attack Limiter");
					return true;
				}

				if (array[1] == "off")
				{
					World.AttackLimiter_Enabled = false;
					HeThongNhacNho("Attack Limiter Disabled", 7, "Attack Limiter");
					return true;
				}

			}
			// Admin command to configure job attack limiter settings
			if (array.Length >= 1 && array[0] == "!jobconfig")
			{
				if (GMMode != 8)
				{
					return false;
				}

				try
				{
					// Show help if no parameters
					if (array.Length == 1)
					{
						HeThongNhacNho("Cách sử dụng: !jobconfig <jobId> <baseCooldown> <maxBonusTime> <minDamage%> <maxDamage%>", 10, "Admin");
						HeThongNhacNho("Ví dụ: !jobconfig 1 1000 1500 10 120", 10, "Admin");
						HeThongNhacNho("Job IDs: 1=Đao, 2=Kiếm, 3=Thương, 4=Cung, 5=Quyền, 6=Ninja", 10, "Admin");
						HeThongNhacNho("         7=Cẩm Sư, 8=Hàn Bảo Quân, 9=Đàm Hoa Liên, 10=Quyền Sư", 10, "Admin");
						HeThongNhacNho("         11=Mai Liều Chân, 12=Tử Hao, 13=Thần Nữ", 10, "Admin");
						return true;
					}

					// Show current config for specific job
					if (array.Length == 2)
					{
						if (int.TryParse(array[1], out int jobId))
						{
							var configInfo = Systems.JobConfig.GetJobConfigInfo(jobId);
							HeThongNhacNho("Cấu hình hiện tại:", 10, "Admin");
							foreach (var line in configInfo.Split('\n'))
							{
								if (!string.IsNullOrEmpty(line.Trim()))
								{
									HeThongNhacNho(line, 10, "Admin");
								}
							}
						}
						else
						{
							HeThongNhacNho("Job ID không hợp lệ!", 20, "Admin");
						}
						return true;
					}

					// Update job config
					if (array.Length == 6)
					{
						if (int.TryParse(array[1], out int jobId) &&
							int.TryParse(array[2], out int baseCooldown) &&
							int.TryParse(array[3], out int maxBonusTime) &&
							int.TryParse(array[4], out int minDamagePercent) &&
							int.TryParse(array[5], out int maxDamagePercent))
						{
							bool success = Systems.JobConfig.UpdateJobConfig(jobId, baseCooldown, maxBonusTime, minDamagePercent, maxDamagePercent);

							if (success)
							{
								var jobName = Systems.JobConfig.GetJobName(jobId);
								HeThongNhacNho($"✅ Đã cập nhật cấu hình cho Job {jobId} ({jobName}) thành công!", 10, "Admin");
								HeThongNhacNho($"Base Cooldown: {baseCooldown}ms", 10, "Admin");
								HeThongNhacNho($"Max Bonus Time: {maxBonusTime}ms", 10, "Admin");
								HeThongNhacNho($"Min Damage: {minDamagePercent}%", 10, "Admin");
								HeThongNhacNho($"Max Damage: {maxDamagePercent}%", 10, "Admin");
							}
							else
							{
								HeThongNhacNho("❌ Cập nhật thất bại! Kiểm tra lại các tham số:", 20, "Admin");
								HeThongNhacNho("- Job ID: 1-13", 20, "Admin");
								HeThongNhacNho("- Base Cooldown: 100-5000ms", 20, "Admin");
								HeThongNhacNho("- Max Bonus Time: >= Base Cooldown và <= 10000ms", 20, "Admin");
								HeThongNhacNho("- Min Damage: 1-100%", 20, "Admin");
								HeThongNhacNho("- Max Damage: 100-300%", 20, "Admin");
							}
						}
						else
						{
							HeThongNhacNho("❌ Tham số không hợp lệ! Tất cả phải là số nguyên.", 20, "Admin");
						}
						return true;
					}

					HeThongNhacNho("❌ Số lượng tham số không đúng!", 20, "Admin");
					HeThongNhacNho("Sử dụng: !jobconfig <jobId> <baseCooldown> <maxBonusTime> <minDamage%> <maxDamage%>", 10, "Admin");
				}
				catch (Exception ex)
				{
					HeThongNhacNho($"❌ Lỗi khi xử lý lệnh jobconfig: {ex.Message}", 20, "Admin");
				}
				return true;
			}

			// Admin command to reload monsters and update AOI for all players
			if (array.Length >= 1 && (array[0] == "@reloadmonster" || array[0] == "!reloadmonster"))
			{
				if (GMMode != 8)
				{
					return false;
				}

				try
				{
					HeThongNhacNho("Bắt đầu reload monster từ database...", 10, "Admin");

					// Step 1: Use current AOI to despawn all NPCs and monsters from clients
					HeThongNhacNho("Đang xóa tất cả NPC và monster khỏi client view...", 10, "Admin");
					int despawnedCount = 0;

					foreach (var player in World.allConnectedChars.Values)
					{
						try
						{
							if (player?.Client != null && player.Client.Running && player.NpcList != null)
							{
								// Find all NPCs and monsters in player's NpcList
								var npcsToRemove = new Dictionary<int, NpcClass>();

								foreach (var npcEntry in player.NpcList.Values)
								{
									// Remove all NPCs and monsters (no filter)
									npcsToRemove.Add(npcEntry.NPC_SessionID, npcEntry);
									var packet = new SendingClass();
									player?.Client.SendPak(packet, 34816, npcEntry.NPC_SessionID);
								}

								// Send UpdateNPC_Despawn to client for all NPCs and monsters
								if (npcsToRemove.Count > 0)
								{
									NpcClass.UpdateNPC_Despawn(npcsToRemove, player);
									despawnedCount += npcsToRemove.Count;
								}

								// Force clear player's NpcList completely
								player.NpcList.Clear();
							}
						}
						catch (Exception ex)
						{
							LogHelper.WriteLine(LogLevel.Error, $"Error despawning NPCs for player {player?.CharacterName}: {ex.Message}");
						}
					}

					// Wait a bit for despawn packets to be processed
					Thread.Sleep(2000);

					HeThongNhacNho($"Đã despawn {despawnedCount} NPC instances từ clients. Đang xóa khỏi server...", 10, "Admin");

					// Step 2: Clear existing NPCs and monsters from all maps
					foreach (var mapEntry in World.MapList.Values)
					{
						var npcsToRemove = new List<NpcClass>();
						foreach (var npc in mapEntry.npcTemplate.Values)
						{
							// Remove all NPCs and monsters (no filter)
							npcsToRemove.Add(npc);
						}

						// Remove all NPCs and monsters from server
						foreach (var npc in npcsToRemove)
						{
							// Remove from AOI system if applicable
							try
							{
								if (AOIConfiguration.Instance.ShouldUseAOI(npc.Rxjh_Map))
								{
									npc.RemoveFromAOI();
								}
							}
							catch (Exception ex)
							{
								LogHelper.WriteLine(LogLevel.Error, $"Error removing NPC from AOI: {ex.Message}");
							}

							// Remove from map using new method
							mapEntry.RemoveNpcFromMapClass(npc.NPC_SessionID);
						}
					}

					// Step 2: Clear global NPC list of all NPCs and monsters
					var globalNpcsToRemove = new List<int>();
					foreach (var npcEntry in World.NpcList)
					{
						// Remove all NPCs and monsters (no filter)
						globalNpcsToRemove.Add(npcEntry.Key);
					}
					foreach (var npcId in globalNpcsToRemove)
					{
						World.NpcList.TryRemove(npcId, out _);
					}

					HeThongNhacNho("Đã xóa tất cả NPC và monster cũ. Đang reload từ database...", 10, "Admin");

					// Step 3: Reload NPCs and monsters from database
					PublicDb.LoadMonsterSetBase();

					HeThongNhacNho("Đã reload NPC và monster thành công. Đang cập nhật AOI cho tất cả players...", 10, "Admin");

					// Step 4: Update AOI for all connected players
					int playerCount = 0;
					foreach (var player in World.allConnectedChars.Values)
					{
						try
						{
							if (player?.Client != null && player.Client.Running)
							{
								// Update AOI using the new system or fallback to old system
								if (AOIConfiguration.Instance.ShouldUseAOI(player.MapID))
								{
									player.UpdateAOIImmediate();
								}
								else
								{
									// Fallback to old system - this will automatically refresh NPCs
									player.GetReviewScopeNpc(true);
								}

								playerCount++;
							}
						}
						catch (Exception ex)
						{
							LogHelper.WriteLine(LogLevel.Error, $"Error updating AOI for player {player?.CharacterName}: {ex.Message}");
						}
					}

					HeThongNhacNho($"Reload NPC và monster hoàn tất! Đã cập nhật AOI cho {playerCount} players.", 10, "Admin");
					LogHelper.WriteLine(LogLevel.Info, $"Admin {CharacterName} đã reload tất cả NPC và monster, cập nhật AOI cho {playerCount} players");

					return true;
				}
				catch (Exception ex)
				{
					HeThongNhacNho($"Lỗi khi reload NPC và monster: {ex.Message}", 20, "Admin");
					LogHelper.WriteLine(LogLevel.Error, $"Error in reload NPC and monster command: {ex.Message}");
					return false;
				}
			}

			if (array.Length >= 2 && (array[0] == "@addspot" || array[0] == "!addspot"))
			{
				if (GMMode != 8)
				{
					return false;
				}
				var pid = int.Parse(array[1]);
				var amount = 1;
				var aoe = 0;
				// addspot pid amount aoe
				if (array.Length >= 3 && int.TryParse(array[2], out var amm))
				{
					amount = amm;
				}
				if (array.Length >= 4 && int.TryParse(array[3], out var aee))
				{
					aoe = aee;
				}
				if (!World.MonsterTemplateList.TryGetValue(pid, out var monster))
				{
					HeThongNhacNho("Không tìm thấy pid này");
					return true;
				}
				float facingx = RNG.Next(-1, 1);
				float facingy = RNG.Next(-1, 1);

				if (pid < 10000)
				{
					World.LoadANpc(pid, monster.fld_name, MapID, facingx, facingy, PosX, PosY, PosZ, true);
					HeThongNhacNho("Add Npc thanh cong");
				}
				else
				{
					for (int i = 0; i < amount; i++)
					{
						float offsetX = RNG.Next(-aoe, aoe);
						float offsetY = RNG.Next(-aoe, aoe);
						float x = PosX + offsetX;
						float y = PosY + offsetY;
						World.LoadAMonster(World.MonsterTemplateList.Count + 1, pid, monster.fld_name, monster.fld_level, monster.fld_exp, monster.FLD_GOLD, monster.fld_npc, monster.fld_hp, (int)monster.fld_at, (int)monster.fld_df, monster.FLD_Accuracy, monster.FLD_Evasion, monster.fld_auto, monster.fld_boss, monster.fld_questitem, 0, 5, MapID, 0, 0, x, y, PosZ, monster.FLD_FreeDrop, true);

					}
					// DBA.ExeSqlCommand(
					// $"INSERT  INTO  TBL_XWWL_MONSTER_SET_BASE(FLD_PID,FLD_X,FLD_Y,FLD_Z,FLD_FACE0,FLD_FACE,FLD_MID,FLD_NAME,FLD_HP,FLD_AT,FLD_DF,FLD_NPC,FLD_NEWTIME,FLD_LEVEL,FLD_EXP,FLD_AUTO,FLD_BOSS,FLD_AMOUNT,FLD_AOE) VALUES ({pid},{PosX},{PosY},{15},{0},{0},{MapID},'{monster.fld_name}',{monster.fld_hp},{monster.fld_at},{monster.fld_df},{0},{5},{monster.fld_level},{monster.fld_exp},{monster.fld_auto},{monster.fld_boss},{amount},{aoe})",
					// "PublicDb").GetAwaiter().GetResult();
					var monsterSetBase = new tbl_xwwl_monster_set_base
					{
						fld_pid = pid,
						fld_x = PosX,
						fld_y = PosY,
						fld_z = 15,
						fld_face0 = 0,
						fld_face = 0,
						fld_mid = MapID,
						fld_name = monster.fld_name,
						fld_hp = monster.fld_hp,
						fld_at = (long)monster.fld_at,
						fld_df = (long)monster.fld_df,
						fld_npc = 0,
						fld_newtime = 5,
						fld_level = monster.fld_level,
						fld_exp = monster.fld_exp,
						fld_auto = monster.fld_auto,
						fld_boss = monster.fld_boss,
						fld_gold = monster.FLD_GOLD,
						fld_accuracy = monster.FLD_Accuracy,
						fld_evasion = monster.FLD_Evasion,
						fld_qitemdrop = monster.fld_questitem,
						fld_qdroppp = 0,
						fld_freedrop = 0,
						fld_active = 1,
						fld_amount = amount,
						fld_aoe = aoe
					};
					PublicDb.InsertMonsterSetBase(monsterSetBase);

					HeThongNhacNho("Add Spot Quai thanh cong");
					return true;
				}
				return false;
			}
			if (array.Length >= 1 && text2 == "!spot")
			{
				if (array.Length == 1)
				{
					FindSpot(Player_Level);
					return true;
				}
				;
				if (array.Length == 2)
				{
					if (int.TryParse(array[1], out var level))
					{

						FindSpot(level);
					}
					else
					{
						HeThongNhacNho("[!spot level spotId] với level là cấp độ, và spotId là số thứ tự của bãi");
					}

					return true;
				}
				if (array.Length >= 3)
				{
					if (!int.TryParse(array[1], out var level) || !int.TryParse(array[2], out var spotId))
					{
						HeThongNhacNho("[!spot level spotId] với level là cấp độ, và spotId là số thứ tự của bãi");
					}
					else
					{
						if (OpenWarehouse || MapID == 2301 || MapID == 2341 ||
							MapID ==
							9001 || MapID == 9101 || MapID == 9201 ||
							NhanVat_HP == 0) return true;
						// if(World.IsRequiredMarketPlace == 1)
						// {
						//         HeThongNhacNho("Mời đến ngân tệ quảng trường truyền tống viên chỗ di động về huyền đột nhiên phái.", 50, "Thiên cơ các");
						//     return true;
						// }
						if (array[1] == "银币广场" || OpenWarehouse || MapID == 2501)
							return true;
						if (Player_Money < 10000)
						{
							HeThongNhacNho("Tiền trò chơi không đủ 10000, không thể di chuyển.");
							return true;
						}

						if (!World.SpotMap.TryGetValue((level, spotId), out var coord))
						{
							HeThongNhacNho("Không tìm thấy spot yêu cầu. Vui lòng tìm spot khác");
							FindSpot(level);
							return true;
						}

						switch (coord.Rxjh_Map)
						{
							case 801:
								HeThongNhacNho("Thế lực chiến trường đồ không thể di động!", 9, "Thiên cơ các");
								return true;
							case 9001 when World.IsHuaMarriageHallInUse:
								{
									if (World.Weddinglist.TryGetValue(9001, out var value19))
									{
										if (value19.Wedding_Progress == 0)
										{
											Mobile(coord.Rxjh_X, coord.Rxjh_Y, coord.Rxjh_Z, coord.Rxjh_Map, 0);
											return true;
										}
										HeThongNhacNho("Chuông hỉ chưa vang, bái đường còn chờ ánh nguyệt rằm.");
										return true;
									}
									HeThongNhacNho("Hỉ yến chưa bày, duyên nợ còn chờ ngày định.");
									return true;
								}
							case 9001:
								HeThongNhacNho("Hỉ yến chưa khai, hồng trần còn đợi giờ lành.");
								return true;
							case 9101 when World.IsTheDragonHallInUse:
								{
									if (World.Weddinglist.TryGetValue(9101, out var value20))
									{
										if (value20.Wedding_Progress == 0)
										{
											Mobile(coord.Rxjh_X, coord.Rxjh_Y, coord.Rxjh_Z, coord.Rxjh_Map, 0);
											return true;
										}
										HeThongNhacNho("Chuông hỉ chưa vang, bái đường còn chờ ánh nguyệt rằm.");
										return true;
									}
									HeThongNhacNho("Hỉ yến chưa bày, duyên nợ còn chờ ngày định.");
									return true;
								}
							case 9101:
								HeThongNhacNho("Hỉ yến chưa khai, hồng trần còn đợi giờ lành.");
								return true;
						}

						if (coord.Rxjh_Map != 9201)
						{
							Mobile(coord.Rxjh_X, coord.Rxjh_Y, coord.Rxjh_Z, coord.Rxjh_Map, 0);
							KiemSoatGold_SoLuong(10000L, 0);
							UpdateMoneyAndWeight();
							return true; ;
						}
						if (World.WhetherTheSacramentalHallIsInUse)
						{
							if (World.Weddinglist.TryGetValue(9201, out var value21))
							{
								if (value21.Wedding_Progress == 0)
								{
									Mobile(coord.Rxjh_X, coord.Rxjh_Y, coord.Rxjh_Z, coord.Rxjh_Map, 0);
									KiemSoatGold_SoLuong(10000L, 0);
									UpdateMoneyAndWeight();
									return true;
								}
								HeThongNhacNho("Chuông hỉ chưa vang, bái đường còn chờ ánh nguyệt rằm.");
								return true;
							}
							HeThongNhacNho("Hỉ yến chưa bày, duyên nợ còn chờ ngày định.");
							return true;
						}
						HeThongNhacNho("Hỉ yến chưa khai, hồng trần còn đợi giờ lành.");
						return true;

					}
				}
				return true;
			}

			if (array.Length >= 1 && array[0] == "!nangbac")
			{
				var item0 = Item_In_Bag[0];
				var item1 = Item_In_Bag[1];
				if (item0.GetVatPham_ID != 0)
				{
					Upgrade_Pet_Item(item0, item1);
				}
				else
				{
					HeThongNhacNho("Đặt vật phẩm cần nâng cấp vào ô đầu tiên!!!");
				}
			}

			var command = array[0];

			switch (command)
			{
				case "!congbo":
					if (GMMode != 8)
						return false;
					if (array.Length >= 2)
					{
						// 4849 nhận thưởng từ npc
						// World.SendMessageCongBo(array[1]);
						if (int.TryParse(array[1], out var ybiId))
						{
							this.SendCongBoTinTuc(ybiId, MapID, CharacterName, *********, 10001);
						}
					}
					break;
				case "!congbo2":
					if (GMMode != 8)
						return false;
					if (array.Length >= 2)
					{
						// 4849 nhận thưởng từ npc
						// World.SendMessageCongBo(array[1]);
						if (int.TryParse(array[1], out var ybiId))
						{
							SendCongBoTinTuc(ybiId, "We extend our warmest congratulations to CEO Phuong Au Tai on this new role. Under the leadership of the new Chairman, TIS is confident to continue its sustainable growth and achieve greater success in the future,We extend our warmest congratulat");
						}
					}
					break;
				case "!setbuff":
					if (GMMode != 8)
						return false;
					if (array.Length >= 2)
					{
						if (int.TryParse(array[1], out var buffId))
						{
							// BuffModule.ForceApplyBuff(buffId);
							var pillTime = 60000;
							if (AppendStatusList.ContainsKey(buffId))
							{
								AppendStatusList[buffId].ThoiGianKetThucSuKien();
							}
							StatusEffect(BitConverter.GetBytes(buffId), 1, pillTime);
							StatusEffect xThemVaoTrangThaiLoai86 = new(this, pillTime, buffId, 1);
							AppendStatusList.Add(xThemVaoTrangThaiLoai86.FLD_PID, xThemVaoTrangThaiLoai86);
							HeThongNhacNho("Set buff thành công");
						}
					}
					break;
				case "!sethacap":
					if (GMMode != 8)
						return false;
					int fLD_FJ_LowSoul = int.Parse(array[1]);
					var item0 = Item_In_Bag[0];
					if (item0.GetVatPham_ID != 0)
					{
						item0.FLD_FJ_LowSoul = fLD_FJ_LowSoul;
						Init_Item_In_Bag();
					}
					HeThongNhacNho("Set ha cap thanh cong");
					break;
				case "!setcuonghoa":
					if (GMMode != 8)
						return false;
					if (array.Length >= 2)
					{
						if (int.TryParse(array[1], out var level))
						{
							if (Item_In_Bag[0].GetVatPham_ID != 0)
							{
								HcItimesClass hcItimesClass36 = new()
								{
									Position = 0,
									VatPham = Item_In_Bag[0].VatPham_byte,
									CuongHoaLoaiHinh = (Item_In_Bag[0].FLD_RESIDE2 == 4) ? 1 : 2,
									CuongHoaSoLuong = level
								};
								hcItimesClass36.ThietLap_GiaiDoanThuocTinh();
								SubtractItem(hcItimesClass36.Position, 1);
								AddItems(hcItimesClass36.ItemGlobal_ID, hcItimesClass36.VatPham_id, hcItimesClass36.Position, hcItimesClass36.VatPhamSoLuong, hcItimesClass36.VatPham_ThuocTinh);
								Init_Item_In_Bag();
								HeThongNhacNho($"Cường hóa thành công {level} !!!", 10, "GM");
								return true;
							}
						}
						else
						{
							HeThongNhacNho("Sai định dạng lệnh!!!", 10, "GM");
						}
					}
					break;
				case "!testreward":
					VerifyCumulativeRewardAsync();
					break;
				case "!reloadcumulative":
					if (GMMode != 8)
						return false;
					World.LoadCumulativeRewardDataAsync().GetAwaiter().GetResult();
					break;
				case "!fakechar":
					if (GMMode != 8)
					{
						return false;
					}
					for (int i = 0; i < 300; i++)
					{
						var randomX = RNG.Next(-100, 100);
						var randomY = RNG.Next(-100, 100);
						var updatedCharacterData = GetUpdatedCharacterData(this, this.PosX + randomX, this.PosY + randomY, this.MapID, SessionID * 2 + i);
						Client?.SendPak(updatedCharacterData, 25600, this.SessionID);
					}
					break;
				case "!wp":
					if (GMMode != 8)
						return false;
					int itemId;
					if (!int.TryParse(array[1], out itemId))
					{
						LogHelper.WriteLine(LogLevel.Error, "Can co Id de add item");
						return false;
					}
					ItmeClass itmeClass = new();
					var itmeClass2 = (itemId != -1) ? ItmeClass.GetItmeID(itemId) : ItmeClass.GetItme(array[1]);
					if (itmeClass2 == null) return true;
					var soLuong = 1;
					if (array.Length >= 3)
					{
						soLuong = int.Parse(array[2]);
					}
					if (soLuong < 1)
					{
						soLuong = 1;
					}
					for (var num48 = 1; num48 <= soLuong; num48++)
					{
						int parcelVacancy2;
						parcelVacancy2 = GetParcelVacancy(this);
						if (parcelVacancy2 != -1)
						{
							ManufacturedItems(parcelVacancy2, BitConverter.GetBytes(itmeClass2.FLD_PID));
							continue;
						}
						break;
					}
					HeThongNhacNho(array[1] + "SL " + soLuong + " cái OK", 10, "GM");
					return true;
				case "!setadmin":
					if (array.Length >= 1)
					{
						// if (Client.ToString() != "127.0.0.1")
						// {
						// 	return true;
						// }
						if (GMMode == 0)
						{
							GMMode = 8;
							if (array.Length >= 2 && array[1] == "vinhvien")
							{
								//DBA.ExeSqlCommand($"UPDATE TBL_XWWL_Char SET FLD_J9=8 WHERE FLD_NAME='{CharacterName}'").GetAwaiter().GetResult();
								GameDb.SetAdmin(CharacterName, 8).GetAwaiter().GetResult();
							}
							HeThongNhacNho("Cấp Quyền Admin thành công!!!", 10, "Thiên cơ các");
						}
						else
						{
							GMMode = 0;
							if (array.Length >= 2 && array[1] == "vinhvien")
							{
								GameDb.SetAdmin(CharacterName, 0).GetAwaiter().GetResult();
							}
							HeThongNhacNho("Hủy Quyền Admin thành công!!!", 20, "Thiên cơ các");
						}
						return true;
					}
					break;
				case "!testoffline":
					{
						World.AutoOffline();
					}
					break;
				case "!wbstart":

					if (GMMode != 8)
						return false;
					HeThongNhacNho("Bắt đầu World Boss !!");
					World.WorldBossEvent.StartWorldBoss();

					break;
				case "!reloadattendance"
					:
					if (GMMode != 8)
						return false;
					HeThongNhacNho("Đang reload attendance data...");
					World.LoadAttendanceDataAsync().GetAwaiter().GetResult();
					HeThongNhacNho("Reload attendance data thành công!!!");
					return true;


				case "!testcrossbosssystem":
					if (GMMode == 8)
					{
						HeThongNhacNho("Bắt đầu test hệ thống Cross Server Boss...");
						_ = Task.Run(async () =>
					{
						try
						{
							await RxjhServer.HeroBoss.CrossServerBossTests.Instance.RunAllTests();
							HeThongNhacNho("Hoàn thành test hệ thống Cross Server Boss!");
						}
						catch (Exception ex)
						{
							HeThongNhacNho($"Lỗi khi test: {ex.Message}");
						}
					});
					}
					break;

				case "!testcrossbossintegration":
					if (GMMode == 8)
					{
						HeThongNhacNho("Bắt đầu integration test Cross Server Boss...");
						_ = Task.Run(async () =>
						{
							try
							{
								await RxjhServer.HeroBoss.CrossServerBossIntegrationTest.RunAllIntegrationTests();
								HeThongNhacNho("Hoàn thành integration test!");
							}
							catch (Exception ex)
							{
								HeThongNhacNho($"Lỗi integration test: {ex.Message}");
							}
						});
					}
					break;

				case "!testcrossbosscomponent":
					if (GMMode == 8 && array.Length > 1)
					{
						var component = array[1];
						HeThongNhacNho($"Bắt đầu test component: {component}...");
						_ = Task.Run(async () =>
						{
							try
							{
								var result = await RxjhServer.HeroBoss.CrossServerBossTests.Instance.TestComponent(component);
								var status = result ? "PASSED" : "FAILED";
								HeThongNhacNho($"{status} Test component {component}");
							}
							catch (Exception ex)
							{
								HeThongNhacNho($"Lỗi test component {component}: {ex.Message}");
							}
						});
					}
					else if (GMMode == 8)
					{
						HeThongNhacNho("Sử dụng: !testcrossbosscomponent <component>");
						HeThongNhacNho("Components: registration, contribution, synchronization, rewards, protocol, cleanup");
					}
					break;
				case "!reloadconfig":
					if (GMMode == 8)
					{
						var configReloadService = new ConfigReloadService();
                        var result = configReloadService.ReloadAsync("all").GetAwaiter().GetResult();
                        HeThongNhacNho($"Reload config result: {result.Message}");
					}
	
				break;
			}
			//if (command.StartsWith("!"))
			//{
			//	var success = GroupQuest.GroupQuestAdminCommands.HandleAdminCommand(this, command.Remove(0, 1), array);
			//	if (success)
			//	{
			//		return true;
			//	}

			//}

		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi sài Lệnh tại num: [" + num + "]");
		}
		return true;
	}

	private void Upgrade_Pet_Item(Item item0, Item item1)
	{
		if (World.List_UpgradeItem.ContainsKey((int)item0.GetVatPham_ID))
		{
			if (item0.GetVatPham_ID == item1.GetVatPham_ID)
			{
				UpgradeItemClass upgradeItemClass = World.List_UpgradeItem[(int)item0.GetVatPham_ID];
				if (upgradeItemClass.NguyenLieu_ID == 0)
				{
					int numx = World.Check_Upgrade_Item((int)item1.GetVatPham_ID);
					if (numx != 0)
					{
						UpgradeItemClass upgradeItemClass2 = World.List_UpgradeItem[numx];
						HeThongNhacNho($"Item Moi {upgradeItemClass2.ItemID}");
						int num2 = new Random(World.GetRandomSeed()).Next(0, 10000);
						HeThongNhacNho("Rate : " + num2 + "/" + upgradeItemClass.Upgrade_PP);
						if (num2 > upgradeItemClass.Upgrade_PP)
						{

							// {
							item0.VatPham_ID = BitConverter.GetBytes(upgradeItemClass2.ItemID);
							//     SubtractItems(1, 1);
							//     SendInventoryBag();
							//var newItem = item0;
							SubtractItem(1, 1);
							//SubtractItem(0, 1);
							AddItems(item0.DatDuocGlobal_ID(), item0.VatPham_ID, 0, BitConverter.GetBytes(1), item0.VatPham_ThuocTinh);
							HeThongNhacNho("Upgrade Pet Success");
							Init_Item_In_Bag();
							//// logo.LogUpgradeItem("[Command] <Success!!!> UserId [" + base.Userid + "] - UserName [" + base.UserName + "] - Old_ItemID [" + upgradeItemClass.ItemID + "] / New_ItemID[" + upgradeItemClass2.ItemID + "] Rate [" + upgradeItemClass.Upgrade_PP + "/" + num2 + "]");
						}
						else
						{
							// SubtractItems(1, 1);
							// SendInventoryBag();
							SubtractItem(1, 1);
							HeThongNhacNho("Upgrade Pet Fail");
							// // logo.LogUpgradeItem("[Command] <Failed!!!> UserId [" + base.Userid + "] - UserName [" + base.UserName + "] - Old_ItemID [" + upgradeItemClass.ItemID + "] Rate [" + upgradeItemClass.Upgrade_PP + "/" + num2 + "]");
						}
					}
					else
					{
						HeThongNhacNho("Vật phẩm đã được nâng cấp tối đa!!!");
					}
				}
				else
				{
					HeThongNhacNho("Vật phẩm cần có nguyên liệu đặc biệt để nâng cấp!!!");
				}
			}
			else
			{
				HeThongNhacNho("Nguyên liệu không phù hợp!!!");
			}
		}
		else
		{
			HeThongNhacNho("Vật phẩm này không thể nâng cấp!!!");
		}
	}

	public void AutoLearnSkill()
	{
		// int i = 0;
		// foreach (var kongfu in World.MagicList.Values)
		// {
		// 	i = kongfu.FLD_PID;
		// 	if (Player_Job != kongfu.FLD_JOB)
		// 		continue;
		// 	if (kongfu.FLD_INDEX <= 0)
		// 		continue;
		// 	if (Player_Job_level < kongfu.FLD_JOBLEVEL)
		// 		continue;
		// 	if (Player_Level < kongfu.FLD_LEVEL)
		// 		continue;
		// 	if (kongfu.FLD_ZX == Player_Zx || Player_Job == 8 || Player_Job == 9 || Player_Job == 13 ||
		// 		Player_Job == 11 || Player_Job == 12)
		// 	{
		// 		HeThongNhacNho("Học :" + kongfu.FLD_VoCongLoaiHinh + " " + kongfu.FLD_INDEX);
		// 		LogHelper.WriteLine(LogLevel.Info, $"AutoLearnSkill {kongfu.FLD_VoCongLoaiHinh} {kongfu.FLD_INDEX}  player {AccountID}-{CharacterName}");
		// 		if (VoCongMoi[kongfu.FLD_VoCongLoaiHinh, kongfu.FLD_INDEX] == null)
		// 		{
		// 			// Sử dụng targetPlayer để học skill
		// 			// X_Vo_Cong_Loai.LearnMartialArtsBook(this,kongfu.FLD_VoCongLoaiHinh, kongfu.FLD_INDEX);
		// 			LearningSkills(kongfu.FLD_VoCongLoaiHinh, kongfu.FLD_INDEX);
		// 		}
		// 	}
		// }
		return;
		//HeThongNhacNho("AUto learn");
		// int i = 0;
		// try
		// {
		// 	// Kiểm tra xem có phải offline player không
		// 	bool isOfflinePlayer = (Client != null && Client.TreoMay) || (Client != null && Client.GetType().Name == "OfflineActorNetState");

		// 	// Đối với offline player, không cần kiểm tra Client.Player vì chính this là Player
		// 	if (!isOfflinePlayer && Client?.Player == null)
		// 	{
		// 		LogHelper.WriteLine(LogLevel.Error, $"AutoLearnSkillError: Client.Player là null cho online player {AccountID}-{CharacterName}");
		// 		return;
		// 	}

		// 	// Lấy reference đến player object
		// 	Players targetPlayer = isOfflinePlayer ? this : Client.Player;

		// 	if (Player_Job == 8)
		// 	{
		// 		if (VoCongMoi[3, 1] == null && Player_Job_level >= 6)
		// 		{
		// 			X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 3, 1);
		// 			X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 1, 10);
		// 		}

		// 		if (VoCongMoi[3, 22] == null && Player_Job_level >= 6)
		// 			X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 3, 22);
		// 		if (VoCongMoi[3, 23] == null && Player_Job_level >= 6)
		// 			X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 3, 23);
		// 		if (VoCongMoi[3, 24] == null && Player_Job_level >= 6)
		// 			X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 3, 24);
		// 	}

		// 	foreach (var kongfu in World.MagicList.Values)
		// 	{
		// 		i = kongfu.FLD_PID;
		// 		if (Player_Job != kongfu.FLD_JOB)
		// 			continue;
		// 		if (kongfu.FLD_INDEX <= 0)
		// 			continue;
		// 		if (Player_Job_level < kongfu.FLD_JOBLEVEL)
		// 			continue;
		// 		if (Player_Level < kongfu.FLD_LEVEL)
		// 			continue;
		// 		if (kongfu.FLD_ZX == Player_Zx || Player_Job == 8 || Player_Job == 9 || Player_Job == 13 ||
		// 			Player_Job == 11 || Player_Job == 12)
		// 			{
		// 				HeThongNhacNho("Học :" + kongfu.FLD_VoCongLoaiHinh + " " + kongfu.FLD_INDEX);
		// 				LogHelper.WriteLine(LogLevel.Info, $"AutoLearnSkill {kongfu.FLD_VoCongLoaiHinh} {kongfu.FLD_INDEX} cho {(isOfflinePlayer ? "offline" : "online")} player {AccountID}-{CharacterName}");
		// 				if (VoCongMoi[kongfu.FLD_VoCongLoaiHinh, kongfu.FLD_INDEX] == null)
		// 				{
		// 					// Sử dụng targetPlayer để học skill
		// 					targetPlayer.LearningSkills(kongfu.FLD_VoCongLoaiHinh, kongfu.FLD_INDEX);
		// 				}
		// 			}
		// 	}

		// 	if (VoCongMoi[1, 2] != null)
		// 		Character_KhinhCong = 2;
		// 	else if (VoCongMoi[1, 1] != null)
		// 		Character_KhinhCong = 1;
		// }
		// catch (Exception e)
		// {
		// 	LogHelper.WriteLine(LogLevel.Error, $"AutoLearnSkillError {i} cho player {AccountID}-{CharacterName}: {e.Message}");
		// }
	}
	private void FindSpot(int monsterLevel)
	{
		HeThongNhacNho($"-- Spot trong khoảng 10 cấp độ từ Level {monsterLevel}", 7, "");

		// Dictionary to store the level and count of spots within range
		Dictionary<int, int> spotsWithinRange = new();

		foreach (var item in World.SpotMap)
		{
			var (mapLevel, _) = item.Key; // Assuming the first int in the tuple is the map level
										  // Check if the spot's level is within 10 levels of the player's level
			if (Math.Abs(monsterLevel - mapLevel) <= 10)
			{
				if (spotsWithinRange.ContainsKey(mapLevel))
				{
					spotsWithinRange[mapLevel]++; // Increase count for this level
				}
				else
				{
					spotsWithinRange.Add(mapLevel, 1); // Initialize count for this level
				}
			}
		}

		// Prepare and send notification for each level with spots within range
		foreach (var level in spotsWithinRange.Keys.OrderBy(l => l))
		{
			string message = $"Level {level}: {spotsWithinRange[level]} spots";
			HeThongNhacNho(message, level == monsterLevel ? 24 : 7, "SPOT");
		}
		HeThongNhacNho("Sử dụng lệnh [!spot x y] để di chuyển tới spot, với x là level của quái, y là số thứ tự của bãi quái", 7, "--");
	}

}