using System;
using System.Collections.Generic;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class Event_Boss_GiangSinh : IDisposable
{
	private System.Timers.Timer ThoiGian1;

	private System.Timers.Timer ThoiGian2;

	private DateTime dateTime_0;

	private DateTime iqOqpBruKS;

	public Event_Boss_GiangSinh()
	{
		try
		{
			World.Event_Noel_Progress = 1;
			World.NpcEvent_GiangSinh.Clear();
			dateTime_0 = DateTime.Now.AddMinutes(5.0);
			ThoiGian1 = new(3000.0);
			ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
			ThoiGian1.Enabled = true;
			ThoiGian1.AutoReset = true;
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				BOSS_Event_Noel();
				{
					foreach (var value in World.allConnectedChars.Values)
					{
						if (!value.Client.TreoMay)
						{
							value.HeThong<PERSON>hac<PERSON>ho("Sự kiện <PERSON> khai mở, đại hiệp có [" + num / 60 + "] khắc để chinh phục Tuần Lộc thần thú!", 10, "Thiên cơ các");
							GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(value, num);
						}
					}
					return;
				}
			}
			ThoiGianKetThucSuKien1(null, null);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BOSS Event Noel Progress = 1 lỗi !! ----------" + ex.Message);
		}
	}

	public void BOSS_Event_Noel()
	{
		try
		{
			AddNpc_SoLuong(15900, 420f, 1740f, 101, 15);
			AddNpc_SoLuong(15900, 156f, 1167f, 101, 15);
			AddNpc_SoLuong(15900, 446f, 1172f, 101, 15);
			AddNpc_SoLuong(15900, 346f, 1184f, 101, 15);
			AddNpc_SoLuong(15900, 50f, 718f, 101, 15);
			AddNpc_SoLuong(15900, -436f, 1301f, 25, 15);
			AddNpc_SoLuong(15900, -1084f, 674f, 101, 15);
			AddNpc_SoLuong(15900, -1550f, 41f, 101, 15);
			AddNpc_SoLuong(15900, -1983f, 1210f, 101, 15);
			AddNpc_SoLuong(15900, -708f, -1021f, 101, 15);
			AddNpc_SoLuong(15900, -280f, -420f, 101, 15);
			AddNpc_SoLuong(15900, -22f, 443f, 101, 15);
			AddNpc_SoLuong(15900, 1588f, 598f, 101, 15);
			AddNpc_SoLuong(15900, 2001f, 1086f, 101, 15);
			AddNpc_SoLuong(15900, 1690f, 1930f, 101, 15);
			AddNpc_SoLuong(15900, 2039f, -159f, 101, 15);
			AddNpc_SoLuong(15900, 948f, -833f, 101, 15);
			AddNpc_SoLuong(15900, 192f, -1319f, 101, 15);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "BOSS Event Noel lỗi !! ----------");
		}
	}

	public void AddNpc_SoLuong(int int_0, float float_0, float float_1, int int_1, int soluong)
	{
		try
		{
			for (var i = 0; i < soluong; i++)
			{
				var num = RNG.Next((int)float_0 - 500, (int)float_0 + 500);
				var num2 = RNG.Next((int)float_1 - 500, (int)float_1 + 500);
				if (World.MonsterTemplateList.TryGetValue(int_0, out var value))
				{
					NpcClass npcClass = new();
					npcClass.FLD_PID = value.fld_pid;
					npcClass.Name = value.fld_name;
					npcClass.Level = value.fld_level;
					npcClass.Rxjh_Exp = value.fld_exp;
					npcClass.Rxjh_X = num;
					npcClass.Rxjh_Y = num2;
					npcClass.Rxjh_Z = 15f;
					npcClass.Rxjh_cs_X = num;
					npcClass.Rxjh_cs_Y = num2;
					npcClass.Rxjh_cs_Z = 15f;
					npcClass.Rxjh_Map = int_1;
					npcClass.IsNpc = 0;
					npcClass.FLD_FACE1 = RNG.Next(-1, 1);
					npcClass.FLD_FACE2 = RNG.Next(-1, 1);
					npcClass.Max_Rxjh_HP = value.fld_hp;
					npcClass.Rxjh_HP = value.fld_hp;
					npcClass.FLD_AT = value.fld_at;
					npcClass.FLD_DF = value.fld_df;
					npcClass.FLD_AUTO = value.fld_auto;
					npcClass.FLD_BOSS = 0;
					npcClass.FLD_NEWTIME = 10;
					npcClass.QuaiXuatHien_DuyNhatMotLan = false;
					npcClass.timeNpc_HoiSinh = DateTime.MinValue;
					if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
					{
						value2.AddNpcToMapClass(npcClass);
					}
					else
					{
						MapClass mapClass = new();
						mapClass.MapID = npcClass.Rxjh_Map;
						mapClass.AddNpcToMapClass(npcClass);
						World.MapList.Add(mapClass.MapID, mapClass);
					}
					npcClass.ScanNearbyPlayer();
					World.NpcEvent_GiangSinh.Add(npcClass.NPC_SessionID, npcClass);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC số lượng - lỗi 44 [" + int_0 + "]error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			ThoiGian1.Enabled = false;
			ThoiGian1.Close();
			ThoiGian1.Dispose();
			World.Event_Noel_Progress = 2;
			iqOqpBruKS = DateTime.Now.AddMinutes(5.0);
			ThoiGian2 = new(3000.0);
			ThoiGian2.Elapsed += ThoiGianKetThucSuKien2;
			ThoiGian2.Enabled = true;
			ThoiGian2.AutoReset = true;
			var num2 = (int)iqOqpBruKS.Subtract(DateTime.Now).TotalSeconds;
			if (num2 <= 0)
			{
				return;
			}
			foreach (var value in World.allConnectedChars.Values)
			{
				if (!value.Client.TreoMay)
				{
					GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(value, num2);
					value.HeThongNhacNho("Sự kiện Tuần Lộc khép lại sau [" + num2 / 60 + "] khắc, hết giờ đại hiệp không thể hoàn thành sứ mệnh!", 10, "Thiên cơ các");
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Event Boss Giáng Sinh ThoiGianKetThucSuKien 222 Phạm sai lầm：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)iqOqpBruKS.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			using (var enumerator = World.allConnectedChars.Values.GetEnumerator())
			{
				if (enumerator.MoveNext())
				{
					var current = enumerator.Current;
					if (!current.Client.TreoMay)
					{
						current.HeThongNhacNho("Sự kiện Tuần Lộc tại cổng thành Huyền Bột hôm nay khép lại, chúc quần hùng Giáng Sinh an lạc!", 10, "Thiên cơ các");
						World.SystemRollingAnnouncement("Sự kiện Tuần Lộc tại cổng thành Huyền Bột đã kết thúc. Chúc các bạn Giáng Sinh vui vẽ!!");
					}
				}
			}
			World.Event_Noel_Progress = 0;
			ThoiGian2.Enabled = false;
			ThoiGian2.Close();
			ThoiGian2.Dispose();
			World.EventNoel.Dispose();
			Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tính toán Boss GiangSinh Event Quái vật còn thừa số lượng phạm sai lầm：" + ex);
		}
	}

	public void Dispose()
	{
		try
		{
			List<NpcClass> list = new();
			foreach (var value in World.NpcEvent_GiangSinh.Values)
			{
				list.Add(value);
			}
			if (list != null)
			{
				foreach (var item in list)
				{
					item.GuiDuLieu_TuVong_MotLanCuaQuaiVat();
				}
				list.Clear();
			}
			World.NpcEvent_GiangSinh.Clear();
			World.CoMoRa_TuyetRoiHayKhong = 0;
			World.Event_Noel_Progress = 0;
			if (ThoiGian1 != null)
			{
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
			}
			if (ThoiGian2 != null)
			{
				ThoiGian2.Enabled = false;
				ThoiGian2.Close();
				ThoiGian2.Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Boss GiangSinh EventKetThuc出错:" + ex.Message);
		}
	}

	public static void GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(Players player, int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101000000000000000000000000000000000000000002EE55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}
}
