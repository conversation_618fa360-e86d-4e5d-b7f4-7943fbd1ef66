
using System;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public partial class Players
{
    public void NotifyAttackRangeError()
    {
        var array = Converter.HexStringToByte("aa55970060050a008d000e4a0000e0270000000000000000000000000000000000000000000000000000000000000000000000000000000000002dc72d00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055aa");
        Buffer.BlockCopy(BitConverter.GetBytes(PosX), 0, array, 0x42, 4);
        Buffer.BlockCopy(BitConverter.GetBytes(PosY), 0, array, 0x4A, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }
    public async void QueueAttackConfirmation(int MyId, int targetID, int time, int damage, int attackType, int skillID, X_Cong_Kich_Loai multiTarget = null)
    {
        try
        {
            if (skill_animation_time <= 0)
            {
                skill_animation_time = World.Time_Animation_Physical;
            }
            var delay = skill_animation_time / 1.3;
            if (skillID != 0)
            {
                if (World.MagicList.TryGetValue(skillID, out var skill))
                {
                    if (skill.FLD_Hit_Times.Count > 0)
                    {
                        delay = skill.FLD_Hit_Times[0];
                    }
                    
                }
            }


            await Task.Delay((int)delay);

            if (targetID > 0)
            {
                AttackConfirmation(targetID);
                if (multiTarget != null)
                {
                    for (int i = 0; i < multiTarget.DanhNhieuMucTieu.Count; i++)
                    {
                        SendDamageNumber(this, multiTarget.DanhNhieuMucTieu[i].NhanVat_ID, multiTarget.DanhNhieuMucTieu[i].CongKichLuc, multiTarget.DanhNhieuMucTieu[i].CongKichLoaiHinh, multiTarget.VoCong_ID, i);
                    }
                }
                else
                {
                    SendDamageNumber(this, targetID, damage, attackType, skillID, 1);
                }
            }
            else
            {
                LogHelper.WriteLine(LogLevel.Error, "Invalid targetID in QueueAttackConfirmation");
            }
        }
        catch (Exception ex)
        {
            AttackList?.Clear();
            LogHelper.WriteLine(LogLevel.Error, $"QueueAttackConfirmation2 Failed: {ex.Message}\n{ex.StackTrace}");
        }
    }

    private void SendDamageNumber(Players Play, int CongKichDoiTuong_CharacterFullServerID, int CongKichLuc, int CongKichLoaiHinh, int VoCongId, int targetNumber = 1)
    {
        var res = Converter.HexStringToByte("aa553a00b001610530007e2800000d00000025000000ffffffff010000000100000081000000000000000000000000000000000000000000000055aa");
        Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, res, 4, 2);
        Buffer.BlockCopy(BitConverter.GetBytes(CongKichDoiTuong_CharacterFullServerID), 0, res, 10, 4);
        Buffer.BlockCopy(BitConverter.GetBytes(0), 0, res, 14, 4);
        Buffer.BlockCopy(BitConverter.GetBytes(CongKichLuc), 0, res, 18, 4);
        Buffer.BlockCopy(BitConverter.GetBytes(targetNumber), 0, res, 26, 4);
        Buffer.BlockCopy(BitConverter.GetBytes(CongKichLoaiHinh), 0, res, 0x22, 2);
        Buffer.BlockCopy(BitConverter.GetBytes(VoCongId), 0, res, 0x2A, 4);
        Play.Client.Send_Map_Data(res, res.Length);
        SendCurrentRangeBroadcastData(res, res.Length);
    }


    public void AttackCalculationCompleted(Players Play, int NhanVat_ID, int VoCong_ID, int CongKichLuc,
        int CongKichLoaiHinh, int SoLuongHPCuoiCung, int LaChan_HapThu)
    {
        var num = 0;
        try
        {
            if (CongKichLuc <= 0)
            {
                CongKichLuc = 0;
            }
            X_Cong_Kich_Loai multiTarget = null;

            LanTanCongCuoiCungVao_NhanVat_ID = NhanVat_ID;
            var pet_CongKichLuc = -1;
            num = 1;
            if (World.MagicList.TryGetValue(VoCong_ID, out var value))
            {
                if (NhanVat_ID >= 10000)
                {
                    if (MapID == 40101 &&
                        World.NpcEvent_DCH.TryGetValue(NhanVat_ID, out var value2) &&
                        (value2.DCH_Tower == 1 || value2.DCH_Tower == 2))
                    {
                        CongKichLuc = DCH_StackA + DCH_StackB + DCH_StackC;
                        if (CongKichLuc < 1)
                        {
                            CongKichLuc = 1;
                        }

                        if (value2.DCH_Tower == 1)
                        {
                            World.DCH_ChinhPhai_DiemSo -= CongKichLuc;
                        }
                        else if (value2.DCH_Tower == 2)
                        {
                            World.DCH_TaPhai_DiemSo -= CongKichLuc;
                        }

                        if (DCH_StackA_SoLuong > 0)
                        {
                            DCH_StackA_SoLuong--;
                            if (DCH_StackA_SoLuong < 1)
                            {
                                DCH_Stack_Add(this, 1008001797, -1, 0);
                                DCH_StackA = 0;
                            }
                            else
                            {
                                DCH_Stack_Add(this, 1008001797, DCH_StackA_SoLuong, 1);
                            }
                        }

                        if (DCH_StackB_SoLuong > 0)
                        {
                            DCH_StackB_SoLuong--;
                            if (DCH_StackB_SoLuong < 1)
                            {
                                DCH_Stack_Add(this, 1008001798, -1, 0);
                                DCH_StackB = 0;
                            }
                            else
                            {
                                DCH_Stack_Add(this, 1008001798, DCH_StackB_SoLuong, 1);
                            }
                        }

                        if (DCH_StackC_SoLuong > 0)
                        {
                            DCH_StackC_SoLuong--;
                            if (DCH_StackC_SoLuong < 1)
                            {
                                DCH_Stack_Add(this, 1008001799, -1, 0);
                                DCH_StackC = 0;
                                UpdateCharacterData(this);
                                UpdateBroadcastCharacterData();
                            }
                            else
                            {
                                DCH_Stack_Add(this, 1008001799, DCH_StackC_SoLuong, 1);
                            }
                        }

                        if (World.DCH_Progress == 4)
                        {
                            if (World.EventTopDCH.TryGetValue(CharacterName, out var value3))
                            {
                                value3.Dame_Tru += CongKichLuc;
                            }
                            else
                            {
                                value3 = new();
                                value3.TenNhanVat = CharacterName;
                                value3.DangCap = Player_Level;
                                value3.TheLuc = DCH_PhePhai;
                                value3.NgheNghiep = Player_Job;
                                value3.BangPhai = GuildName;
                                value3.Full_Server_ID = SessionID;
                                value3.GietNguoiSoLuong = 0;
                                value3.TuVongSoLuong = 1;
                                World.EventTopDCH.Add(CharacterName, value3);
                            }
                        }
                        else
                        {
                            HeThongNhacNho("Thời gian kết thúc, không thu được điểm số Đại Chiến Huyền 2!", 20, "Thiên cơ các");
                        }
                    }

                    num = 2;
                    checkkepskill = false;
                    X_Cong_Kich_Loai x_Cong_Kich_Loai;
                    if (value.FLD_TYPE != 4 && !PhatDong_LuuTinhManThien && !TriggerKillingStarYiqiTiger &&
                        !TriggerKillingStarLoyaltyKill)
                    {
                        num = 201;
                        if (MapClass.GetNpc(MapID, NhanVat_ID) == null)
                        {
                            return;
                        }

                        num = 212;
                        if (CharacterBeast != null)
                        {
                            pet_CongKichLuc = (CharacterBeast.FLD_JOB_LEVEL == 1
                                ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                                   CharacterBeast.VoCongMoi[0, 1].FLD_AT)
                                : ((CharacterBeast.FLD_JOB_LEVEL == 2)
                                    ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                                       CharacterBeast.VoCongMoi[0, 2].FLD_AT)
                                    : ((CharacterBeast.FLD_JOB_LEVEL != 3)
                                        ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich)
                                        : (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                                           CharacterBeast.VoCongMoi[0, 3].FLD_AT))));
                        }

                        num = 223;
                        x_Cong_Kich_Loai = new(NhanVat_ID, VoCong_ID, CongKichLuc, value.FLD_EFFERT);
                        SendAttackerData(Play, NhanVat_ID, VoCong_ID, CongKichLuc, value.FLD_EFFERT, SoLuongHPCuoiCung,
                            LaChan_HapThu, pet_CongKichLuc);
                    }
                    else
                    {
                        var num2 = value.FLD_CongKichSoLuong;
                        if (PhatDong_LuuVanCuongKich)
                        {
                            num2 = 5;
                        }

                        if (PhatDong_LuuTinhManThien)
                        {
                            num2 = 5;
                        }

                        if (TriggerKillingStarYiqiTiger)
                        {
                            num2 = 5;
                        }

                        if (TriggerKillingStarLoyaltyKill && value.FLD_TYPE == 4)
                        {
                            TriggerKillingStarLoyaltyKill = false;
                        }

                        if (Play.Player_Job == 1)
                        {
                            if (Play.LuuQuang_LoanVu >= 0.02 && Play.LuuQuang_LoanVu < 0.05)
                            {
                                num2++;
                            }
                        }
                        else if (Play.Player_Job == 7)
                        {
                            var num3 = 0.0;
                            if (Play.GetAddState(900403))
                            {
                                var cAMSU_NhacDuongTamTuy = Play.CAMSU_NhacDuongTamTuy;
                                if (cAMSU_NhacDuongTamTuy > 0.0)
                                {
                                    num2 += (int)(0.45 + cAMSU_NhacDuongTamTuy * 0.2);
                                }

                                if (Play.PhatDong_LoanPhuongHoaMinh)
                                {
                                    num2 *= 2;
                                }
                            }

                            var cAMSU_NhacDuongTamTuy2 = Play.CAMSU_NhacDuongTamTuy;
                            if (cAMSU_NhacDuongTamTuy2 > 0.0)
                            {
                                num3 = cAMSU_NhacDuongTamTuy2 * 0.01;
                            }

                            var cAMSU_ThangThien_3_KhiCong_TuDaThuCa = Play.CAMSU_ThangThien_3_KhiCong_TuDaThuCa;
                            if (cAMSU_ThangThien_3_KhiCong_TuDaThuCa > 0.0)
                            {
                                num3 = cAMSU_ThangThien_3_KhiCong_TuDaThuCa * 0.01;
                            }

                            CongKichLuc = (int)(CongKichLuc * (1.0 + num3));
                        }
                        else if (Play.Player_Job == 8)
                        {
                            if (Play.LuuQuang_LoanVu >= 0.02 && Play.LuuQuang_LoanVu < 0.05)
                            {
                                num2++;
                            }
                        }
                        else if (Play.Player_Job == 13)
                        {
                            CongKichLuc = (int)(CongKichLuc * (1.0 + ThanNu_ThanLucKichPhat));
                        }

                        x_Cong_Kich_Loai = ((Play.Player_Job == 13 && TriggerKillingStarYiqiTiger)
                            ? new(NhanVat_ID, VoCong_ID, CongKichLuc, value.FLD_EFFERT, value.FLD_TYPE,
                                TriggerKillingStarYiqiTiger)
                            : ((Play.Player_Job != 13 || !TriggerKillingStarLoyaltyKill)
                                ? new(NhanVat_ID, VoCong_ID, CongKichLuc, value.FLD_EFFERT,
                                    value.FLD_TYPE, PhatDong_LuuTinhManThien)
                                : new X_Cong_Kich_Loai(NhanVat_ID, VoCong_ID, CongKichLuc, value.FLD_EFFERT,
                                    value.FLD_TYPE, TriggerKillingStarLoyaltyKill)));
                        var npc = MapClass.GetNpc(MapID, NhanVat_ID);
                        if (npc == null)
                        {
                            return;
                        }

                        x_Cong_Kich_Loai.DanhNhieuMucTieu.Add(
                            new(NhanVat_ID, VoCong_ID, CongKichLuc, value.FLD_EFFERT)
                            {
                                SoLuongHPConDuLai = npc.Rxjh_HP,
                                TongSoLuongHP = npc.Max_Rxjh_HP
                            });
                        num = 27;
                        var list = npc.DanhNhieuMucTieu_TraTimPhamVi_Npc2(this, num2);
                        //HeThongNhacNho("Danh nhieu muc tieu npc");
                        var num4 = 0;
                        num4 = (Player_Job != 12) ? (CongKichLuc - CongKichLuc / 3) : CongKichLuc;
                        Random random = new();
                        if (list != null && list.Count > 0)
                        {
                            foreach (var item in list)
                            {
                                num4 = random.Next(num4 - 15, num4 + 15);
                                x_Cong_Kich_Loai.DanhNhieuMucTieu.Add(
                                    new(item.NPC_SessionID, VoCong_ID, num4, value.FLD_EFFERT)
                                    {
                                        SoLuongHPConDuLai = item.Rxjh_HP,
                                        TongSoLuongHP = item.Max_Rxjh_HP
                                    });
                                if (x_Cong_Kich_Loai.DanhNhieuMucTieu.Count >= num2)
                                {
                                    break;
                                }
                            }
                        }

                        if (CharacterBeast != null)
                        {
                            pet_CongKichLuc = ((CharacterBeast.FLD_JOB_LEVEL == 1)
                                ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                                   CharacterBeast.VoCongMoi[0, 1].FLD_AT)
                                : ((CharacterBeast.FLD_JOB_LEVEL == 2)
                                    ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                                       CharacterBeast.VoCongMoi[0, 2].FLD_AT)
                                    : ((CharacterBeast.FLD_JOB_LEVEL != 3)
                                        ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich)
                                        : (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                                           CharacterBeast.VoCongMoi[0, 3].FLD_AT))));
                        }
                        multiTarget = x_Cong_Kich_Loai;
                        BuildAttackMultiTargetRes(Play, x_Cong_Kich_Loai.DanhNhieuMucTieu, NhanVat_ID, VoCong_ID,
                            CongKichLuc, value.FLD_EFFERT, pet_CongKichLuc);
                    }

                    AttackList.Clear();
                    CongKich_XacNhan_SoLan = 1;
                    using (new Lock(AttackList, "AttackList"))
                    {
                        AttackList.Add(x_Cong_Kich_Loai);
                    }
                    QueueAttackConfirmation(SessionID, NhanVat_ID, 1500, CongKichLuc, CongKichLoaiHinh, VoCong_ID, multiTarget);
                    //SendPack(Play.SessionID, NhanVat_ID, 0, value.FLD_PID, 1500);
                    return;
                }

                checkkepskill = true;
                if (!PhatDong_LuuTinhManThien && !TriggerKillingStarYiqiTiger && !TriggerKillingStarLoyaltyKill &&
                    !Trigger_NhatDiemNguThanh)
                {
                    if (CharacterBeast != null)
                    {
                        pet_CongKichLuc = ((CharacterBeast.FLD_JOB_LEVEL == 1)
                            ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                               CharacterBeast.VoCongMoi[0, 1].FLD_AT)
                            : ((CharacterBeast.FLD_JOB_LEVEL == 2)
                                ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                                   CharacterBeast.VoCongMoi[0, 2].FLD_AT)
                                : ((CharacterBeast.FLD_JOB_LEVEL != 3)
                                    ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich)
                                    : (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                                       CharacterBeast.VoCongMoi[0, 3].FLD_AT))));
                    }

                    SendAttackerData(Play, NhanVat_ID, VoCong_ID, CongKichLuc, value.FLD_EFFERT, SoLuongHPCuoiCung,
                        LaChan_HapThu, pet_CongKichLuc);
                }
                else
                {
                    var num5 = value.FLD_CongKichSoLuong;
                    if (Trigger_NhatDiemNguThanh)
                    {
                        num5 = 1;
                    }
                    else if (PhatDong_LuuTinhManThien)
                    {
                        num5 = 5;
                    }

                    if (CharacterBeast != null)
                    {
                        pet_CongKichLuc = ((CharacterBeast.FLD_JOB_LEVEL == 1)
                            ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                               CharacterBeast.VoCongMoi[0, 1].FLD_AT)
                            : ((CharacterBeast.FLD_JOB_LEVEL == 2)
                                ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                                   CharacterBeast.VoCongMoi[0, 2].FLD_AT)
                                : ((CharacterBeast.FLD_JOB_LEVEL != 3)
                                    ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich)
                                    : (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                                       CharacterBeast.VoCongMoi[0, 3].FLD_AT))));
                    }

                    var x_Cong_Kich_Loai2 = ((Play.Player_Job == 13 && TriggerKillingStarYiqiTiger)
                        ? new(Play.SessionID, VoCong_ID, CongKichLuc, value.FLD_EFFERT,
                            value.FLD_TYPE, TriggerKillingStarYiqiTiger)
                        : ((Play.Player_Job != 13 || !TriggerKillingStarLoyaltyKill)
                            ? new(Play.SessionID, VoCong_ID, CongKichLuc, value.FLD_EFFERT,
                                value.FLD_TYPE, PhatDong_LuuTinhManThien)
                            : new X_Cong_Kich_Loai(Play.SessionID, VoCong_ID, CongKichLuc, value.FLD_EFFERT,
                                value.FLD_TYPE, TriggerKillingStarLoyaltyKill)));
                    if (World.allConnectedChars.TryGetValue(NhanVat_ID, out var value4))
                    {
                        x_Cong_Kich_Loai2.DanhNhieuMucTieu.Add(
                            new(NhanVat_ID, VoCong_ID, CongKichLuc, value.FLD_EFFERT)
                            {
                                SoLuongHPConDuLai = value4.NhanVat_HP,
                                TongSoLuongHP = value4.CharacterMax_HP
                            });
                    }

                    var list2 = GroupAttackSearchRangeRW2(this, num5);
                    HeThongNhacNho("Danh nhieu muc tieu player");
                    var num6 = 0;
                    num6 = (Player_Job != 12) ? (CongKichLuc - CongKichLuc / 3) : CongKichLuc;
                    Random random2 = new();
                    if (list2 != null && list2.Count > 0)
                    {
                        foreach (var item2 in list2)
                        {
                            if (item2.SessionID != NhanVat_ID)
                            {
                                num6 = random2.Next(num6 - 15, num6 + 15);
                                x_Cong_Kich_Loai2.DanhNhieuMucTieu.Add(
                                    new(item2.SessionID, VoCong_ID, num6,
                                        value.FLD_EFFERT)
                                    {
                                        SoLuongHPConDuLai = item2.NhanVat_HP,
                                        TongSoLuongHP = item2.CharacterMax_HP
                                    });
                                if (x_Cong_Kich_Loai2.DanhNhieuMucTieu.Count >= num5)
                                {
                                    break;
                                }
                            }
                        }
                    }

                    BuildAttackMultiTargetRes(Play, x_Cong_Kich_Loai2.DanhNhieuMucTieu, NhanVat_ID, VoCong_ID,
                        CongKichLuc, value.FLD_EFFERT, pet_CongKichLuc);
                }

                AttackList.Clear();
                CongKich_XacNhan_SoLan = 1;
                using (new Lock(AttackList, "AttackList"))
                {
                    AttackList.Add(new(NhanVat_ID, VoCong_ID, CongKichLuc, value.FLD_EFFERT));
                }

                //SendPack(Play.SessionID, NhanVat_ID, 0, value.FLD_PID, 500);
                QueueAttackConfirmation(SessionID, NhanVat_ID, 500, CongKichLuc, CongKichLoaiHinh, VoCong_ID, multiTarget);
                return;
            }

            if (MapID == 40101 && World.NpcEvent_DCH.TryGetValue(NhanVat_ID, out var value5) &&
                (value5.DCH_Tower == 1 || value5.DCH_Tower == 2))
            {
                CongKichLuc = DCH_StackA + DCH_StackB + DCH_StackC;
                if (CongKichLuc < 1)
                {
                    CongKichLuc = 1;
                }

                if (value5.DCH_Tower == 1)
                {
                    World.DCH_ChinhPhai_DiemSo -= CongKichLuc;
                }
                else if (value5.DCH_Tower == 2)
                {
                    World.DCH_TaPhai_DiemSo -= CongKichLuc;
                }

                if (DCH_StackA_SoLuong > 0)
                {
                    DCH_StackA_SoLuong--;
                    if (DCH_StackA_SoLuong < 1)
                    {
                        DCH_Stack_Add(this, 1008001797, -1, 0);
                        DCH_StackA = 0;
                    }
                    else
                    {
                        DCH_Stack_Add(this, 1008001797, DCH_StackA_SoLuong, 1);
                    }
                }

                if (DCH_StackB_SoLuong > 0)
                {
                    DCH_StackB_SoLuong--;
                    if (DCH_StackB_SoLuong < 1)
                    {
                        DCH_Stack_Add(this, 1008001798, -1, 0);
                        DCH_StackB = 0;
                    }
                    else
                    {
                        DCH_Stack_Add(this, 1008001798, DCH_StackB_SoLuong, 1);
                    }
                }

                if (DCH_StackC_SoLuong > 0)
                {
                    DCH_StackC_SoLuong--;
                    if (DCH_StackC_SoLuong < 1)
                    {
                        DCH_Stack_Add(this, 1008001799, -1, 0);
                        DCH_StackC = 0;
                        UpdateCharacterData(this);
                        UpdateBroadcastCharacterData();
                    }
                    else
                    {
                        DCH_Stack_Add(this, 1008001799, DCH_StackC_SoLuong, 1);
                    }
                }

                if (World.DCH_Progress == 4)
                {
                    if (World.EventTopDCH.TryGetValue(CharacterName, out var value6))
                    {
                        value6.Dame_Tru += CongKichLuc;
                    }
                    else
                    {
                        value6 = new();
                        value6.TenNhanVat = CharacterName;
                        value6.DangCap = Player_Level;
                        value6.TheLuc = DCH_PhePhai;
                        value6.NgheNghiep = Player_Job;
                        value6.BangPhai = GuildName;
                        value6.Full_Server_ID = SessionID;
                        value6.GietNguoiSoLuong = 0;
                        value6.TuVongSoLuong = 1;
                        World.EventTopDCH.Add(CharacterName, value6);
                    }
                }
                else
                {
                    HeThongNhacNho("Thời gian kết thúc, không thu được điểm số Đại Chiến Huyền 3!", 20, "Thiên cơ các");
                }
            }

            num = 41;
            if (CharacterBeast != null)
            {
                pet_CongKichLuc = ((CharacterBeast.FLD_JOB_LEVEL == 1)
                    ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                       CharacterBeast.VoCongMoi[0, 1].FLD_AT)
                    : ((CharacterBeast.FLD_JOB_LEVEL == 2)
                        ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                           CharacterBeast.VoCongMoi[0, 2].FLD_AT)
                        : ((CharacterBeast.FLD_JOB_LEVEL != 3)
                            ? (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich)
                            : (CharacterBeast.FLD_CongKich + CharacterBeast.FLD_TrungDich +
                               CharacterBeast.VoCongMoi[0, 3].FLD_AT))));
            }

            CongKich_XacNhan_SoLan = 1;
            if (CongKichLoaiHinh == 128 || CongKichLoaiHinh == 129 || CongKichLoaiHinh == 130 ||
                CongKichLoaiHinh == 131 || CongKichLoaiHinh == 132 || CongKichLoaiHinh == 133 ||
                CongKichLoaiHinh == 136)
            {
                CongKich_XacNhan_SoLan = 3;
            }

            SendAttackerData(Play, NhanVat_ID, VoCong_ID, CongKichLuc, CongKichLoaiHinh, SoLuongHPCuoiCung,
                LaChan_HapThu, pet_CongKichLuc);
            AttackList.Clear();
            using (new Lock(AttackList, "AttackList"))
            {
                AttackList.Add(new(NhanVat_ID, VoCong_ID, CongKichLuc, CongKichLoaiHinh));
            }

            var times_ = Play.Player_Job switch
            {
                4 => 1000,
                3 or 5 => 950,
                6 => 500,
                7 => 800,
                11 => 1000,
                12 => 950,
                13 => 950,
                _ => 700,
            };
            if (Play.Player_Job == 6)
            {
                times_ = (!Play.GetAddState(801201) ? 500 : 100);
                if (CongKichLoaiHinh == 129 || CongKichLoaiHinh == 130 || CongKichLoaiHinh == 131 ||
                    CongKichLoaiHinh == 132 || CongKichLoaiHinh == 133)
                {
                    times_ = 300;
                }
            }
            else if (Play.Player_Job == 9 && VoCong_ID == 2000402 && NhanVat_ID >= 10000)
            {
                times_ = 300;
            }

            if (Play.Item_Wear[3].GetVatPham_ID == 0)
            {
                times_ = 720;
            }

            //SendPack(Play.SessionID, NhanVat_ID, CongKichLoaiHinh, VoCong_ID, times_);
            QueueAttackConfirmation(SessionID, NhanVat_ID, times_, CongKichLuc, CongKichLoaiHinh, VoCong_ID);
        }
        catch (Exception ex)
        {
            QuyenSu_KiemSoat_ComBo = 0;
            AttackList.Clear();
            LogHelper.WriteLine(LogLevel.Error,
                "Attack Calculation Lỗi tại num: [" + num + "][" + AccountID + "][" + CharacterName + "] -CharJob:[" +
                Player_Job + "][" + Client.ToString() + "][" + VoCong_ID + "][" + NhanVat_ID + "] - " +
                ex.Message);
            LogHelper.WriteLine(LogLevel.Error, ex.StackTrace);
        }
    }


}
