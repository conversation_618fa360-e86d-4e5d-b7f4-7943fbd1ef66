using System;
using System.Timers;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using RxjhServer.Database;


namespace RxjhServer;

public class EvePVPClass : IDisposable
{
	private ThreadSafeDictionary<int, Players> playlist = new();

	private System.Timers.Timer ThoiGian1;

	private System.Timers.Timer ThoiGian2;

	private System.Timers.Timer ThoiGian3;

	private System.Timers.Timer ThoiGian4;

	private System.Timers.Timer ThoiGian5;

	private System.Timers.Timer ThoiGian6;

	private DateTime kssj;

	private DateTime kssjgj;

	private int kssjint;

	private Players PlayerA;

	private Players PlayerB;

	public EvePVPClass(ThreadSafeDictionary<int, Players> players)
	{
		try
		{
			foreach (var value in players.Values)
			{
				playlist.Add(value.SessionID, value);
				if (PlayerA == null)
				{
					PlayerA = value;
					PlayerA.PVPScore = 0;
					PlayerA.PVPEscapes = 0;
				}
				else if (PlayerB == null)
				{
					PlayerB = value;
					PlayerB.PVPScore = 0;
					PlayerB.PVPEscapes = 0;
				}
			}
			kssj = DateTime.Now.AddMinutes(3.0);
			World.Eve90Progress = 1;
			ThoiGian1 = new(60000.0);
			ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
			ThoiGian1.Enabled = true;
			ThoiGian1.AutoReset = true;
			ThoiGianKetThucSuKien1(null, null);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "PVP EveClass error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
			if (num <= 0)
			{
				World.Eve90Progress = 2;
				num = 0;
			}
			kssjint = num;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (!value.Client.TreoMay)
				{
					value.HeThongNhacNho("[" + PlayerA.CharacterName + "] cùng [" + PlayerB.CharacterName + "] sắp tỷ thí võ công, Thiên Cơ Các sau " + kssjint + " giây sẽ phân định thắng thua!", 2, "Truyền Âm Các");
					value.HeThongNhacNho("Quần hùng dùng bí lệnh [!data] hoặc [!datb] để đặt cược, số lượng cho phép là " + World.SoLuongChoPhep_NguoiChoiDatCuoc + " điểm!", 13, "Truyền Âm Các");
				}
			}
			if (kssjint <= 0)
			{
				if (PlayerA.CheckIfThePlayerIsInTheDuelZone(PlayerA))
				{
					PlayerA.Mobile(120f, 0f, 15f, 2301, 0);
				}
				if (PlayerB.CheckIfThePlayerIsInTheDuelZone(PlayerB))
				{
					PlayerB.Mobile(120f, 0f, 15f, 2301, 0);
				}
				// PlayerA.SystemNotification("Bắt đầu tỉ số...");
				// PlayerB.SystemNotification("Bắt đầu tỉ số...");
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
				World.Eve90Progress = 3;
				kssjgj = DateTime.Now.AddMinutes(10.0);
				ThoiGian2 = new(10000.0);
				ThoiGian2.Elapsed += ThoiGianKetThucSuKien2;
				ThoiGian2.Enabled = true;
				ThoiGian2.AutoReset = true;
				ThoiGian6 = new(1000.0);
				ThoiGian6.Elapsed += ThoiGianKetThucSuKien6;
				ThoiGian6.Enabled = true;
				ThoiGian6.AutoReset = true;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "PVP ThoiGianKetThucSuKien1 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = World.Eve90_ThoiGian = (int)kssjgj.Subtract(DateTime.Now).TotalSeconds;
			foreach (var value in playlist.Values)
			{
				value.HeThongNhacNho(PlayerA.CharacterName + "[" + PlayerA.PVPScore + "] đối chiến " + PlayerB.CharacterName + " [" + PlayerA.PVPScore + "], tỷ thí khép lại sau " + World.Eve90_ThoiGian + " giây!", 13, "Truyền Âm Các");
			}
			if (num <= 0 || !PlayerA.Client.Running || !PlayerB.Client.Running)
			{
				ThoiGian2.Enabled = false;
				ThoiGian2.Close();
				ThoiGian2.Dispose();
				ThoiGian6.Enabled = false;
				ThoiGian6.Close();
				ThoiGian6.Dispose();
				World.Eve90Progress = 4;
				ThoiGian3 = new(10000.0);
				ThoiGian3.Elapsed += ThoiGianKetThucSuKien3;
				ThoiGian3.Enabled = true;
				ThoiGian3.AutoReset = false;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "PVP ThoiGianKetThucSuKien2 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien3(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)(World.PhiVaoCua_ToiThieu * 2 * (1.0 - World.SanTapTienThue_TiLePhanTram));
			if (!PlayerA.Client.Running && !PlayerB.Client.Running)
			{
				foreach (var value in World.allConnectedChars.Values)
				{
					if (!value.Client.TreoMay)
					{
						value.HeThongNhacNho("Thật đáng tiếc! Một bên đã rời trận, tỷ thí chấm dứt, giang hồ xôn xao!", 7, "Truyền Âm Các");
					}
				}
				GameDb.InsertPvpLog("90", PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, 0, 0, "双方弃权").GetAwaiter().GetResult();
				//DBA.ExeSqlCommand(string.Format("INSERT INTO TBL_XWWL_PVP (SanTapTen,A_NguoiChoi,B_NguoiChoi,AGietNguoiSoLuong,BGietNguoiSoLuong,A_ChayTronSoLan,B_ChayTronSoLan,AThuDuocNguyenBao,BThuDuocNguyenBao,TranhTaiKetQua)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')", 90, PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, 0, 0, "双方弃权"));
			}
			else if (!PlayerA.Client.Running)
			{
				PlayerB.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
				PlayerB.KiemSoatNguyenBao_SoLuong(num, 1);
				PlayerB.Save_NguyenBaoData();
				foreach (var value2 in World.allConnectedChars.Values)
				{
					if (!value2.Client.TreoMay)
					{
						value2.HeThongNhacNho("Thật đáng tiếc! [" + PlayerA.CharacterName + "] giữa trận bỏ chạy, [" + PlayerB.CharacterName + "] giành chiến thắng, tỷ thí khép lại!", 2, "Truyền Âm Các");
					}
				}
				//DBA.ExeSqlCommand(string.Format("INSERT INTO TBL_XWWL_PVP (SanTapTen,A_NguoiChoi,B_NguoiChoi,AGietNguoiSoLuong,BGietNguoiSoLuong,A_ChayTronSoLan,B_ChayTronSoLan,AThuDuocNguyenBao,BThuDuocNguyenBao,TranhTaiKetQua)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')", 90, PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, 0, num, PlayerA.CharacterName + " ThoatGame比赛"));
				GameDb.InsertPvpLog("90", PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, 0, num, PlayerA.CharacterName + " ThoatGame比赛").GetAwaiter().GetResult();
				GameDb.SetPlayerHonorPoints(2, PlayerB.CharacterName, PlayerB.Player_Job, PlayerB.Player_Level, PlayerB.Player_Zx, PlayerB.GuildName, string.Empty, PlayerB.PVPScore);
			}
			else if (!PlayerB.Client.Running)
			{
				PlayerA.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
				PlayerA.KiemSoatNguyenBao_SoLuong(num, 1);
				PlayerA.Save_NguyenBaoData();
				foreach (var value3 in World.allConnectedChars.Values)
				{
					if (!value3.Client.TreoMay)
					{
						value3.HeThongNhacNho("Thật đáng tiếc! [" + PlayerB.CharacterName + "] giữa trận bỏ chạy, [" + PlayerA.CharacterName + "] giành chiến thắng, tỷ thí khép lại!", 2, "Truyền Âm Các");
					}
				}
				//DBA.ExeSqlCommand(string.Format("INSERT INTO TBL_XWWL_PVP (SanTapTen,A_NguoiChoi,B_NguoiChoi,AGietNguoiSoLuong,BGietNguoiSoLuong,A_ChayTronSoLan,B_ChayTronSoLan,AThuDuocNguyenBao,BThuDuocNguyenBao,TranhTaiKetQua)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')", 90, PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, num, 0, PlayerB.CharacterName + " ThoatGame比赛"));
				GameDb.InsertPvpLog("90", PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, num, 0, PlayerB.CharacterName + " ThoatGame比赛").GetAwaiter().GetResult();
				GameDb.SetPlayerHonorPoints(2, PlayerA.CharacterName, PlayerA.Player_Job, PlayerA.Player_Level, PlayerA.Player_Zx, PlayerA.GuildName, string.Empty, PlayerA.PVPScore);
			}
			else if (PlayerA.PVPScore > PlayerB.PVPScore)
			{
				PlayerA.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
				PlayerA.KiemSoatNguyenBao_SoLuong(num, 1);
				PlayerA.Save_NguyenBaoData();
				foreach (var value4 in World.allConnectedChars.Values)
				{
					if (!value4.Client.TreoMay)
					{
						value4.HeThongNhacNho("Tỷ thí cao cấp khép lại, [" + PlayerA.CharacterName + "] [" + PlayerA.PVPScore + "] đối [" + PlayerB.CharacterName + "] [" + PlayerB.PVPScore + "] chiến thắng, nhận [" + num + "] điểm!", 2, "Truyền Âm Các");
					}
				}
				GameDb.InsertPvpLog("90", PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, num, 0, PlayerA.CharacterName + " Chiến thắng").GetAwaiter().GetResult();
				//DBA.ExeSqlCommand(string.Format("INSERT INTO TBL_XWWL_PVP (SanTapTen,A_NguoiChoi,B_NguoiChoi,AGietNguoiSoLuong,BGietNguoiSoLuong,A_ChayTronSoLan,B_ChayTronSoLan,AThuDuocNguyenBao,BThuDuocNguyenBao,TranhTaiKetQua)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')", 90, PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, num, 0, PlayerA.CharacterName + " Chiến thắng"));
				GameDb.SetPlayerHonorPoints(2, PlayerA.CharacterName, PlayerA.Player_Job, PlayerA.Player_Level, PlayerA.Player_Zx, PlayerA.GuildName, string.Empty, PlayerA.PVPScore);
			}
			else if (PlayerA.PVPScore == PlayerB.PVPScore)
			{
				if (PlayerA.PVPEscapes > PlayerB.PVPEscapes)
				{
					PlayerB.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
					PlayerB.KiemSoatNguyenBao_SoLuong(num, 1);
					PlayerB.Save_NguyenBaoData();
					foreach (var value5 in World.allConnectedChars.Values)
					{
						if (!value5.Client.TreoMay)
						{
							value5.HeThongNhacNho("Tỷ thí cao cấp khép lại, hòa nhau vì " + PlayerA.CharacterName + " bỏ chạy quá nhiều, Thiên Cơ Các phán " + PlayerB.CharacterName + " chiến thắng, nhận " + num + " điểm!", 2, "Truyền Âm Các");
						}
					}
					GameDb.InsertPvpLog("90", PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, 0, num, "DiemSo相同," + PlayerB.CharacterName + " 获Thang").GetAwaiter().GetResult();
					//DBA.ExeSqlCommand(string.Format("INSERT INTO TBL_XWWL_PVP (SanTapTen,A_NguoiChoi,B_NguoiChoi,AGietNguoiSoLuong,BGietNguoiSoLuong,A_ChayTronSoLan,B_ChayTronSoLan,AThuDuocNguyenBao,BThuDuocNguyenBao,TranhTaiKetQua)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')", 90, PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, 0, num, "DiemSo相同," + PlayerB.CharacterName + " 获Thang"));
					GameDb.SetPlayerHonorPoints(2, PlayerB.CharacterName, PlayerB.Player_Job, PlayerB.Player_Level, PlayerB.Player_Zx, PlayerB.GuildName, string.Empty, PlayerB.PVPScore);
				}
				else if (PlayerA.PVPEscapes == PlayerB.PVPEscapes)
				{
					PlayerA.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
					PlayerA.KiemSoatNguyenBao_SoLuong((int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)), 1);
					PlayerA.Save_NguyenBaoData();
					PlayerB.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
					PlayerB.KiemSoatNguyenBao_SoLuong((int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)), 1);
					PlayerB.Save_NguyenBaoData();
					foreach (var value6 in World.allConnectedChars.Values)
					{
						if (!value6.Client.TreoMay)
						{
							value6.HeThongNhacNho("Tỷ thí khép lại, đôi bên bất phân thắng bại, giang hồ kinh ngạc!", 2, "Truyền Âm Các");
						}
					}
					GameDb.InsertPvpLog("90", PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, (int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)), (int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)), "Hoa局").GetAwaiter().GetResult();
					//DBA.ExeSqlCommand(string.Format("INSERT INTO TBL_XWWL_PVP (SanTapTen,A_NguoiChoi,B_NguoiChoi,AGietNguoiSoLuong,BGietNguoiSoLuong,A_ChayTronSoLan,B_ChayTronSoLan,AThuDuocNguyenBao,BThuDuocNguyenBao,TranhTaiKetQua)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')", 90, PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, (int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)), (int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)), "Hoa局"));
					GameDb.SetPlayerHonorPoints(2, PlayerA.CharacterName, PlayerA.Player_Job, PlayerA.Player_Level, PlayerA.Player_Zx, PlayerA.GuildName, string.Empty, PlayerA.PVPScore);
					GameDb.SetPlayerHonorPoints(2, PlayerB.CharacterName, PlayerB.Player_Job, PlayerB.Player_Level, PlayerB.Player_Zx, PlayerB.GuildName, string.Empty, PlayerB.PVPScore);
				}
				else
				{
					PlayerA.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
					PlayerA.KiemSoatNguyenBao_SoLuong(num, 1);
					PlayerA.Save_NguyenBaoData();
					foreach (var value7 in World.allConnectedChars.Values)
					{
						if (!value7.Client.TreoMay)
						{
							value7.HeThongNhacNho("Tỷ thí cao cấp khép lại, hòa nhau vì " + PlayerB.CharacterName + " bỏ chạy quá nhiều, Thiên Cơ Các phán " + PlayerA.CharacterName + " chiến thắng, nhận " + num + " điểm!", 2, "Truyền Âm Các");
						}
					}
					GameDb.InsertPvpLog("90", PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, num, 0, "DiemSo相同," + PlayerA.CharacterName + " 获Thang").GetAwaiter().GetResult();
					//DBA.ExeSqlCommand(string.Format("INSERT INTO TBL_XWWL_PVP (SanTapTen,A_NguoiChoi,B_NguoiChoi,AGietNguoiSoLuong,BGietNguoiSoLuong,A_ChayTronSoLan,B_ChayTronSoLan,AThuDuocNguyenBao,BThuDuocNguyenBao,TranhTaiKetQua)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')", 90, PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, num, 0, "DiemSo相同," + PlayerA.CharacterName + " 获Thang"));
					GameDb.SetPlayerHonorPoints(2, PlayerA.CharacterName, PlayerA.Player_Job, PlayerA.Player_Level, PlayerA.Player_Zx, PlayerA.GuildName, string.Empty, PlayerA.PVPScore);
				}
			}
			else
			{
				PlayerB.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
				PlayerB.KiemSoatNguyenBao_SoLuong(num, 1);
				PlayerB.Save_NguyenBaoData();
				foreach (var value8 in World.allConnectedChars.Values)
				{
					if (!value8.Client.TreoMay)
					{
						value8.HeThongNhacNho("Tỷ thí cao cấp khép lại, [" + PlayerB.CharacterName + "][" + PlayerB.PVPScore + "] đối [" + PlayerA.CharacterName + "][" + PlayerA.PVPScore + "] chiến thắng, nhận " + num + " điểm!", 2, "Truyền Âm Các");
					}
				}
				GameDb.InsertPvpLog("90", PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, 0, num, PlayerB.CharacterName + " 获Thang").GetAwaiter().GetResult();
				//DBA.ExeSqlCommand(string.Format("INSERT INTO TBL_XWWL_PVP (SanTapTen,A_NguoiChoi,B_NguoiChoi,AGietNguoiSoLuong,BGietNguoiSoLuong,A_ChayTronSoLan,B_ChayTronSoLan,AThuDuocNguyenBao,BThuDuocNguyenBao,TranhTaiKetQua)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')", 90, PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, 0, num, PlayerB.CharacterName + " 获Thang"));
				GameDb.SetPlayerHonorPoints(2, PlayerB.CharacterName, PlayerB.Player_Job, PlayerB.Player_Level, PlayerB.Player_Zx, PlayerB.GuildName, string.Empty, PlayerB.PVPScore);
			}
			var num2 = World.TinhToan_NguoiChoiDatCuoc_KetQua(PlayerA.PVPScore, PlayerB.PVPScore, 90);
			foreach (var value9 in World.allConnectedChars.Values)
			{
				if (!value9.Client.TreoMay)
				{
					value9.HeThongNhacNho("Trận đấu thông thường khép lại, con số may mắn trong trận là: " + num2 + string.Empty, 3, "Truyền Âm Các");
				}
			}
			World.Eve90Progress = 5;
			kssjgj = DateTime.Now.AddMinutes(1.0);
			ThoiGian3.Enabled = false;
			ThoiGian3.Close();
			ThoiGian3.Dispose();
			ThoiGian4 = new(30000.0);
			ThoiGian4.Elapsed += ThoiGianKetThucSuKien4;
			ThoiGian4.Enabled = true;
			ThoiGian4.AutoReset = true;
			ThoiGian5 = new(30000.0);
			ThoiGian5.Elapsed += ThoiGianKetThucSuKien5;
			ThoiGian5.Enabled = true;
			ThoiGian5.AutoReset = true;
			ThoiGian6.Enabled = false;
			ThoiGian6.Close();
			ThoiGian6.Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "PVP ThoiGianKetThucSuKien3 error：" + ex);
			Dispose();
		}
	}

	public void ThoiGianKetThucSuKien4(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((World.Eve90_ThoiGian = (int)kssjgj.Subtract(DateTime.Now).TotalSeconds) <= 0 || !PlayerA.Client.Running || !PlayerB.Client.Running)
			{
				ThoiGian4.Enabled = false;
				ThoiGian4.Close();
				ThoiGian4.Dispose();
				World.Eve90Progress = 6;
				Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "PVP ThoiGianKetThucSuKien4 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien5(object sender, ElapsedEventArgs e)
	{
		try
		{
			ThoiGian5.Enabled = false;
			ThoiGian5.Close();
			ThoiGian5.Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "PVP ThoiGianKetThucSuKien5 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien6(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (PlayerA.Client == null || !PlayerA.Client.Running || PlayerB.Client == null || !PlayerB.Client.Running)
			{
				return;
			}
			if (PlayerA.CheckIfThePlayerIsInTheDuelZone(PlayerA))
			{
				if (PlayerA.CharacterPKMode != 2)
				{
					PlayerA.SwitchPkMode(2);
				}
			}
			else
			{
				if (PlayerA.PVPEscapes > World.SoLanTronThoat_ChoPhep)
				{
					if (PlayerA.PVPScore > 0)
					{
						PlayerA.PVPScore--;
						PlayerA.HeThongNhacNho("Đại hiệp đã bỏ chạy [" + PlayerA.PVPEscapes + "] lần, vượt giới hạn [" + World.SoLanTronThoat_ChoPhep + "] lần, bị Thiên Cơ Các khấu trừ một phần!", 13, "Truyền Âm Các");
					}
					else if (PlayerA.PVPScore == 0)
					{
						PlayerA.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
						if (PlayerA.FLD_RXPIONT >= World.NguyenBaoBiTru_SauKhiTruDiem)
						{
							PlayerA.KiemSoatNguyenBao_SoLuong(World.NguyenBaoBiTru_SauKhiTruDiem, 0);
							PlayerA.Save_NguyenBaoData();
							PlayerA.HeThongNhacNho("Đại hiệp điểm số bằng 0, đã bỏ chạy [" + PlayerA.PVPEscapes + "] lần, vượt giới hạn [" + World.SoLanTronThoat_ChoPhep + "] lần, bị khấu trừ [" + World.NguyenBaoBiTru_SauKhiTruDiem + "] điểm!", 13, "Truyền Âm Các");
						}
						else if (PlayerA.Player_Money >= World.TienBiTru_SauKhiTruDiem)
						{
							PlayerA.Player_Money -= World.TienBiTru_SauKhiTruDiem;
							PlayerA.UpdateMoneyAndWeight();
							PlayerA.HeThongNhacNho("Đại hiệp điểm số bằng 0, đã bỏ chạy [" + PlayerA.PVPEscapes + "] lần, vượt giới hạn [" + World.SoLanTronThoat_ChoPhep + "] lần, bị khấu trừ [" + World.TienBiTru_SauKhiTruDiem + "] ngân lượng!", 13, "Truyền Âm Các");
						}
					}
				}
				else if (PlayerA.PVPEscapes >= 5)
				{
					PlayerA.HeThongNhacNho("Đại hiệp đã bỏ chạy [" + PlayerA.PVPEscapes + "] lần, vượt [" + World.SoLanTronThoat_ChoPhep + "] lần, sau mỗi lần chạy trốn sẽ bị khấu trừ. Điểm số bằng 0 sẽ trừ điểm hoặc ngân lượng!", 13, "Truyền Âm Các");
				}
				PlayerA.PVPEscapes++;
				PlayerA.Mobile(120f, 0f, 15f, 2301, 0);
			}
			if (PlayerB.CheckIfThePlayerIsInTheDuelZone(PlayerB))
			{
				if (PlayerB.CharacterPKMode != 2)
				{
					PlayerB.SwitchPkMode(2);
				}
			}
			else
			{
				if (PlayerB.PVPEscapes > World.SoLanTronThoat_ChoPhep)
				{
					if (PlayerB.PVPScore > 0)
					{
						PlayerB.PVPScore--;
						PlayerB.HeThongNhacNho("Đại hiệp đã bỏ chạy [" + PlayerB.PVPEscapes + "] lần, vượt giới hạn [" + World.SoLanTronThoat_ChoPhep + "] lần, bị Thiên Cơ Các khấu trừ một phần!", 13, "Truyền Âm Các");
					}
					else if (PlayerB.PVPScore == 0)
					{
						PlayerB.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
						if (PlayerB.FLD_RXPIONT >= World.NguyenBaoBiTru_SauKhiTruDiem)
						{
							PlayerB.KiemSoatNguyenBao_SoLuong(World.NguyenBaoBiTru_SauKhiTruDiem, 0);
							PlayerB.Save_NguyenBaoData();
							PlayerB.HeThongNhacNho("Đại hiệp điểm số bằng 0, đã bỏ chạy [" + PlayerB.PVPEscapes + "] lần, vượt giới hạn [" + World.SoLanTronThoat_ChoPhep + "] lần, bị khấu trừ [" + World.NguyenBaoBiTru_SauKhiTruDiem + "] điểm!", 13, "Truyền Âm Các");
						}
						else if (PlayerB.Player_Money >= World.TienBiTru_SauKhiTruDiem)
						{
							PlayerB.Player_Money -= World.TienBiTru_SauKhiTruDiem;
							PlayerB.UpdateMoneyAndWeight();
							PlayerB.HeThongNhacNho("Đại hiệp điểm số bằng 0, đã bỏ chạy [" + PlayerB.PVPEscapes + "] lần, vượt giới hạn [" + World.SoLanTronThoat_ChoPhep + "] lần, bị khấu trừ [" + World.TienBiTru_SauKhiTruDiem + "] điểm!", 13, "Truyền Âm Các");
						}
					}
				}
				else if (PlayerB.PVPEscapes >= 5)
				{
					PlayerB.HeThongNhacNho("Đại hiệp đã bỏ chạy [" + PlayerB.PVPEscapes + "] lần, vượt [" + World.SoLanTronThoat_ChoPhep + "] lần, sau mỗi lần chạy trốn sẽ bị khấu trừ. Điểm số bằng 0 sẽ trừ điểm hoặc ngân lượng!", 13, "Truyền Âm Các");
				}
				PlayerB.PVPEscapes++;
				PlayerB.Mobile(120f, 0f, 15f, 2301, 0);
			}
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == 2301 && value.SessionID != PlayerA.SessionID && value.SessionID != PlayerB.SessionID)
				{
					value.Mobile(529f, 1528f, 15f, 101, 0);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "PVP ThoiGianKetThucSuKien6 error：" + ex);
			Dispose();
		}
	}

	public void Dispose()
	{
		World.Eve90Progress = 0;
		World.Eve90_ThoiGian = 0;
		if (ThoiGian1 != null)
		{
			ThoiGian1.Enabled = false;
			ThoiGian1.Close();
			ThoiGian1.Dispose();
		}
		if (ThoiGian2 != null)
		{
			ThoiGian2.Enabled = false;
			ThoiGian2.Close();
			ThoiGian2.Dispose();
		}
		if (ThoiGian3 != null)
		{
			ThoiGian3.Enabled = false;
			ThoiGian3.Close();
			ThoiGian3.Dispose();
		}
		if (ThoiGian4 != null)
		{
			ThoiGian4.Enabled = false;
			ThoiGian4.Close();
			ThoiGian4.Dispose();
		}
		if (ThoiGian6 != null)
		{
			ThoiGian6.Enabled = false;
			ThoiGian6.Close();
			ThoiGian6.Dispose();
		}
		foreach (var value in World.allConnectedChars.Values)
		{
			if (value.MapID == 2301)
			{
				value.Mobile(529f, 1528f, 15f, 101, 0);
				value.SwitchPkMode(0);
			}
		}
		PlayerA = null;
		PlayerB = null;
		World.evePlayers.Clear();
		World.evePlayers = null;
		World.EVEPVP = null;
	}
}
