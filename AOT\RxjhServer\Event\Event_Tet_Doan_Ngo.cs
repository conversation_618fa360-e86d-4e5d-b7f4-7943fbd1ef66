using System;
using System.Collections.Generic;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class Event_Tet_Doan_Ngo
{
	private System.Timers.Timer ThoiGian1;

	private System.Timers.Timer ThoiGian2;

	private DateTime dateTime_0;

	private DateTime iqOqpBruKS;

	public Event_Tet_Doan_Ngo()
	{
		try
		{
			World.Event_TetDoanNgo_Progress = 1;
			World.Npc_TetDoanNgo.Clear();
			dateTime_0 = DateTime.Now.AddMinutes(5.0);
			ThoiGian1 = new(3000.0);
			ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
			ThoiGian1.Enabled = true;
			ThoiGian1.AutoReset = true;
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				Call_Monster_TetDoanNgo();
				{
					foreach (var value in World.allConnectedChars.Values)
					{
						if (!value.Client.TreoMay)
						{
							value.HeThong<PERSON>hac<PERSON>ho("Sự kiện Tết <PERSON> - Diệt Sâu <PERSON> khai mở, đại hiệp có [" + num / 60 + "] khắc để tiêu diệt lũ yêu trùng!", 10, "Thiên cơ các");
							GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(value, num);
						}
					}
					return;
				}
			}
			ThoiGianKetThucSuKien1(null, null);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Event Tet Doan Ngo Progress = 1 lỗi !! ----------" + ex.Message);
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			ThoiGian1.Enabled = false;
			ThoiGian1.Close();
			ThoiGian1.Dispose();
			World.Event_TetDoanNgo_Progress = 2;
			iqOqpBruKS = DateTime.Now.AddMinutes(5.0);
			ThoiGian2 = new(3000.0);
			ThoiGian2.Elapsed += ThoiGianKetThucSuKien2;
			ThoiGian2.Enabled = true;
			ThoiGian2.AutoReset = true;
			var num2 = (int)iqOqpBruKS.Subtract(DateTime.Now).TotalSeconds;
			if (num2 <= 0)
			{
				return;
			}
			foreach (var value in World.allConnectedChars.Values)
			{
				if (!value.Client.TreoMay)
				{
					GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(value, num2);
					value.HeThongNhacNho("Sự kiện khép lại sau [" + num2 / 60 + "] khắc, hết giờ đại hiệp không thu được bảo vật!", 10, "Thiên cơ các");
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Event Tết Đoan Ngọ 222 Phạm sai lầm：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)iqOqpBruKS.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			using (var enumerator = World.allConnectedChars.Values.GetEnumerator())
			{
				if (enumerator.MoveNext())
				{
					var current = enumerator.Current;
					if (!current.Client.TreoMay)
					{
						current.HeThongNhacNho("Sự kiện Diệt Sâu Bọ - Tết Đoan Ngọ tại cổng thành Huyền Bột đã khép lại, quần hùng an lạc!", 10, "Thiên cơ các");
						World.SystemRollingAnnouncement("Sự kiện Diệt Sâu Bọ - Tết Đoan Ngọ tại cổng thành Huyền Bột đã kết thúc");
					}
				}
			}
			World.Event_TetDoanNgo_Progress = 0;
			ThoiGian2.Enabled = false;
			ThoiGian2.Close();
			ThoiGian2.Dispose();
			World.TetDoanNgo_DietSauBo_Event.Dispose();
			Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tính toán Hái Thuốc Event còn thừa số lượng phạm sai lầm：" + ex);
		}
	}

	public static void GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(Players player, int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101000000000000000000000000000000000000000002EE55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void Call_Monster_TetDoanNgo()
	{
		try
		{
			AddNpc_SoLuong_Random_ViTri(15236, 284f, 1187f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 88f, 754f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -65f, 432f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -333f, -324f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -650f, -661f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -1330f, -960f, 25, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -1595f, -506f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -1550f, 60f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -1204f, 392f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -1334f, 1041f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -306f, 1455f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -343f, 1985f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 1500f, 2146f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 1541f, 1442f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 1346f, 1071f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 1341f, 681f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 1708f, 717f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 1863f, 133f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 2082f, -260f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 1678f, -773f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 994f, -791f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 509f, -1197f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 136f, -1292f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 636f, -640f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, 976f, 463f, 101, 10);
			AddNpc_SoLuong_Random_ViTri(15236, -350f, 400f, 101, 10);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "BOSS Event Noel lỗi !! ----------");
		}
	}

	public void AddNpc_SoLuong_Random_ViTri(int int_0, float float_0, float float_1, int int_1, int soluong)
	{
		try
		{
			for (var i = 0; i < soluong; i++)
			{
				var num = RNG.Next((int)float_0 - 200, (int)float_0 + 200);
				var num2 = RNG.Next((int)float_1 - 200, (int)float_1 + 200);
				if (World.MonsterTemplateList.TryGetValue(int_0, out var value))
				{
					NpcClass npcClass = new();
					npcClass.FLD_PID = value.fld_pid;
					npcClass.Name = value.fld_name;
					npcClass.Level = value.fld_level;
					npcClass.Rxjh_Exp = value.fld_exp;
					npcClass.Rxjh_X = num;
					npcClass.Rxjh_Y = num2;
					npcClass.Rxjh_Z = 15f;
					npcClass.Rxjh_cs_X = num;
					npcClass.Rxjh_cs_Y = num2;
					npcClass.Rxjh_cs_Z = 15f;
					npcClass.Rxjh_Map = int_1;
					npcClass.IsNpc = 0;
					npcClass.FLD_FACE1 = RNG.Next(-1, 1);
					npcClass.FLD_FACE2 = RNG.Next(-1, 1);
					npcClass.Max_Rxjh_HP = value.fld_hp;
					npcClass.Rxjh_HP = value.fld_hp;
					npcClass.FLD_AT = value.fld_at;
					npcClass.FLD_DF = value.fld_df;
					npcClass.FLD_AUTO = value.fld_auto;
					npcClass.FLD_BOSS = 0;
					npcClass.FLD_NEWTIME = 100000;
					npcClass.QuaiXuatHien_DuyNhatMotLan = true;
					npcClass.timeNpc_HoiSinh = DateTime.MinValue;
					if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
					{
						value2.AddNpcToMapClass(npcClass);
					}
					else
					{
						MapClass mapClass = new();
						mapClass.MapID = npcClass.Rxjh_Map;
						mapClass.AddNpcToMapClass(npcClass);
						World.MapList.Add(mapClass.MapID, mapClass);
					}
					npcClass.ScanNearbyPlayer();
					World.Npc_TetDoanNgo.Add(npcClass.NPC_SessionID, npcClass);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC số lượng - lỗi 44 [" + int_0 + "]error：" + ex);
		}
	}

	public void AddNpc_SoLuong_ViTri_CongThanh_HBP(int int_0, float float_0, float float_1, int int_1, int soluong)
	{
		try
		{
			for (var i = 0; i < soluong; i++)
			{
				var num = RNG.Next((int)float_0 - 100, (int)float_0 + 100);
				var num2 = RNG.Next((int)float_1 - 100, (int)float_1 + 100);
				if (World.MonsterTemplateList.TryGetValue(int_0, out var value))
				{
					NpcClass npcClass = new();
					npcClass.FLD_PID = value.fld_pid;
					npcClass.Name = value.fld_name;
					npcClass.Level = value.fld_level;
					npcClass.Rxjh_Exp = value.fld_exp;
					npcClass.Rxjh_X = num;
					npcClass.Rxjh_Y = num2;
					npcClass.Rxjh_Z = 15f;
					npcClass.Rxjh_cs_X = num;
					npcClass.Rxjh_cs_Y = num2;
					npcClass.Rxjh_cs_Z = 15f;
					npcClass.Rxjh_Map = int_1;
					npcClass.IsNpc = 0;
					npcClass.FLD_FACE1 = 0f;
					npcClass.FLD_FACE2 = 0f;
					npcClass.Max_Rxjh_HP = value.fld_hp;
					npcClass.Rxjh_HP = value.fld_hp;
					npcClass.FLD_AT = value.fld_at;
					npcClass.FLD_DF = value.fld_df;
					npcClass.FLD_AUTO = value.fld_auto;
					npcClass.FLD_BOSS = 0;
					npcClass.FLD_NEWTIME = 5;
					npcClass.QuaiXuatHien_DuyNhatMotLan = false;
					npcClass.timeNpc_HoiSinh = DateTime.MinValue;
					if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
					{
						value2.AddNpcToMapClass(npcClass);
					}
					else
					{
						MapClass mapClass = new();
						mapClass.MapID = npcClass.Rxjh_Map;
						mapClass.AddNpcToMapClass(npcClass);
						World.MapList.Add(mapClass.MapID, mapClass);
					}
					npcClass.ScanNearbyPlayer();
					World.Npc_TetDoanNgo.Add(npcClass.NPC_SessionID, npcClass);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC Quỷ Trùng 15236 số lượng - lỗi [" + int_0 + "]error：" + ex);
		}
	}

	public void Dispose()
	{
		try
		{
			List<NpcClass> list = new();
			foreach (var value in World.Npc_TetDoanNgo.Values)
			{
				list.Add(value);
			}
			if (list != null)
			{
				foreach (var item in list)
				{
					item.GuiDuLieu_TuVong_MotLanCuaQuaiVat();
				}
				list.Clear();
			}
			World.Npc_TetDoanNgo.Clear();
			World.Event_TetDoanNgo_Progress = 0;
			if (ThoiGian1 != null)
			{
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
			}
			if (ThoiGian2 != null)
			{
				ThoiGian2.Enabled = false;
				ThoiGian2.Close();
				ThoiGian2.Dispose();
			}
			World.TetDoanNgo_DietSauBo_Event = null;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Dispose Quỷ Trùng 15236 - lỗi !! - " + ex.Message);
		}
	}
}
