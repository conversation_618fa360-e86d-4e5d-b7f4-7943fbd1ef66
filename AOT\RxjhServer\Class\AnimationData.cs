using System;
using System.Collections.Generic;

public static class AnimationData
{
    public static Dictionary<(int job, int effectId, int zx), AnimationInfo> Data = new Dictionary<(int job, int effectId, int zx), AnimationInfo>
    {
        { (4, 206, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 790, 5520 }, HitEndTimes = new int[] { 1120, 6310 }, FileName = "AMCA0206.ani" } },
        { (4, 207, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3280, 6640 }, HitEndTimes = new int[] { 4340, 7420 }, FileName = "AMCA0207.ani" } },
        { (4, 208, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2820 }, HitEndTimes = new int[] { 6240 }, FileName = "AMCA0208.ani" } },
        { (4, 209, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2760, 5120, 7030 }, HitEndTimes = new int[] { 3350, 5580, 7550 }, FileName = "AMCA0209.ani" } },
        { (4, 300, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1910, 6440 }, HitEndTimes = new int[] { 3350, 7230 }, FileName = "AMCA0300.ani" } },
        { (4, 301, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 130, 2100, 6440 }, HitEndTimes = new int[] { 460, 2370, 7290 }, FileName = "AMCA0301.ani" } },
        { (4, 302, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2170 }, HitEndTimes = new int[] { 4400 }, FileName = "AMCA0302.ani" } },
        { (4, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4340 }, HitEndTimes = new int[] { 6700 }, FileName = "AMCA0303.ani" } },
        { (4, 304, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5120 }, HitEndTimes = new int[] { 6110 }, FileName = "AMCA0304.ani" } },
        { (4, 305, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3150 }, HitEndTimes = new int[] { 7100 }, FileName = "AMCA0305.ani" } },
        { (4, 306, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 660, 5190 }, HitEndTimes = new int[] { 2230, 6770 }, FileName = "AMCA0306.ani" } },
        { (4, 307, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2170, 5190 }, HitEndTimes = new int[] { 3810, 7360 }, FileName = "AMCA0307.ani" } },
        { (4, 308, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3550, 6370 }, HitEndTimes = new int[] { 4800, 7100 }, FileName = "AMCA0308.ani" } },
        { (4, 309, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1050, 3350, 6700 }, HitEndTimes = new int[] { 1710, 3940, 7290 }, FileName = "AMCA0309.ani" } },
        { (4, 310, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3550, 5190, 6500 }, HitEndTimes = new int[] { 4070, 5720, 7230 }, FileName = "AMCA0310.ani" } },
        { (4, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 960, 4350, 6590 }, HitEndTimes = new int[] { 1920, 5380, 8190 }, FileName = "AMCA0311.ani" } },
        { (4, 312, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 900, 6980 }, HitEndTimes = new int[] { 2620, 7870 }, FileName = "AMCA0312.ani" } },
        { (4, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2620 }, HitEndTimes = new int[] { 7100 }, FileName = "AMCA0313.ani" } },
        { (4, 314, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7040 }, HitEndTimes = new int[] { 8190 }, FileName = "AMCA0314.ani" } },
        { (4, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4533 }, HitEndTimes = new int[] { 5799 }, FileName = "AMCA0315.ani" } },
        { (4, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1530 }, HitEndTimes = new int[] { 5340 }, FileName = "AMCA0317.ani" } },
        { (4, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4078 }, HitEndTimes = new int[] { 4806 }, FileName = "AMCA0318.ani" } },
        { (4, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4510 }, HitEndTimes = new int[] { 4655 }, FileName = "AMCA0319.ani" } },
        { (4, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5721 }, HitEndTimes = new int[] { 6110 }, FileName = "AMCA0326.ani" } },
        { (4, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6020 }, HitEndTimes = new int[] { 6590 }, FileName = "AMCA0327.ani" } },
        { (4, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3150 }, HitEndTimes = new int[] { 5430 }, FileName = "AMCA0330.ani" } },
        { (4, 338, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2230 }, HitEndTimes = new int[] { 5870 }, FileName = "AMCA0338.ani" } },
        { (4, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4330 }, HitEndTimes = new int[] { 4490 }, FileName = "AMCA0341.ani" } },
        { (4, 406, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 850 }, HitEndTimes = new int[] { 2240 }, FileName = "AMCA0406.ani" } },
        { (4, 407, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1860 }, HitEndTimes = new int[] { 3050 }, FileName = "AMCA0407.ani" } },
        { (4, 408, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 880 }, HitEndTimes = new int[] { 2680 }, FileName = "AMCA0408.ani" } },
        { (4, 409, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 410, 2340 }, HitEndTimes = new int[] { 880, 2810 }, FileName = "AMCA0409.ani" } },
        { (4, 500, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 70, 1460 }, HitEndTimes = new int[] { 510, 2310 }, FileName = "AMCA0500.ani" } },
        { (4, 501, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 30, 1560 }, HitEndTimes = new int[] { 440, 2340 }, FileName = "AMCA0501.ani" } },
        { (4, 502, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 30 }, HitEndTimes = new int[] { 610 }, FileName = "AMCA0502.ani" } },
        { (4, 503, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 370 }, HitEndTimes = new int[] { 2810 }, FileName = "AMCA0503.ani" } },
        { (4, 504, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1420 }, HitEndTimes = new int[] { 2270 }, FileName = "AMCA0504.ani" } },
        { (4, 505, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 310 }, HitEndTimes = new int[] { 2980 }, FileName = "AMCA0505.ani" } },
        { (4, 506, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 370, 1930 }, HitEndTimes = new int[] { 1320, 3260 }, FileName = "AMCA0506.ani" } },
        { (4, 507, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 1660 }, HitEndTimes = new int[] { 510, 3290 }, FileName = "AMCA0507.ani" } },
        { (4, 508, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1700, 3150 }, HitEndTimes = new int[] { 2510, 3490 }, FileName = "AMCA0508.ani" } },
        { (4, 509, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 340, 2540 }, HitEndTimes = new int[] { 850, 3150 }, FileName = "AMCA0509.ani" } },
        { (4, 510, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3090 }, HitEndTimes = new int[] { 3700 }, FileName = "AMCA0510.ani" } },
        { (4, 511, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 630 }, HitEndTimes = new int[] { 1950 }, FileName = "AMCA0511.ani" } },
        { (4, 512, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2440 }, HitEndTimes = new int[] { 3110 }, FileName = "AMCA0512.ani" } },
        { (4, 513, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1950 }, HitEndTimes = new int[] { 3170 }, FileName = "AMCA0513.ani" } },
        { (3, 202, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2590, 8320 }, HitEndTimes = new int[] { 2900, 8540 }, FileName = "AMCB0202.ani" } },
        { (3, 206, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1680, 5810 }, HitEndTimes = new int[] { 2390, 7180 }, FileName = "AMCB0206.ani" } },
        { (3, 207, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6050 }, HitEndTimes = new int[] { 6950 }, FileName = "AMCB0207.ani" } },
        { (3, 208, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 4930 }, HitEndTimes = new int[] { 2620, 5950 }, FileName = "AMCB0208.ani" } },
        { (3, 209, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2230, 6040 }, HitEndTimes = new int[] { 2690, 6310 }, FileName = "AMCB0209.ani" } },
        { (3, 300, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2510, 3770, 7070 }, HitEndTimes = new int[] { 3240, 4690, 7600 }, FileName = "AMCB0300.ani" } },
        { (3, 301, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2630, 5030 }, HitEndTimes = new int[] { 3230, 5330 }, FileName = "AMCB0301.ani" } },
        { (3, 302, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1500, 2870, 7000 }, HitEndTimes = new int[] { 2690, 4010, 8800 }, FileName = "AMCB0302.ani" } },
        { (3, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6850 }, HitEndTimes = new int[] { 9340 }, FileName = "AMCB0303.ani" } },
        { (3, 304, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2220, 6110 }, HitEndTimes = new int[] { 4970, 8560 }, FileName = "AMCB0304.ani" } },
        { (3, 305, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 7300 }, HitEndTimes = new int[] { 3710, 8900 }, FileName = "AMCB0305.ani" } },
        { (3, 306, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3010, 6660 }, HitEndTimes = new int[] { 4740, 8510 }, FileName = "AMCB0306.ani" } },
        { (3, 307, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2300, 4480, 7040 }, HitEndTimes = new int[] { 3390, 6340, 9090 }, FileName = "AMCB0307.ani" } },
        { (3, 308, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 750, 2920, 5220 }, HitEndTimes = new int[] { 1090, 3390, 5630 }, FileName = "AMCB0308.ani" } },
        { (3, 309, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2370, 4680, 6580 }, HitEndTimes = new int[] { 2780, 5560, 6920 }, FileName = "AMCB0309.ani" } },
        { (3, 310, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1640, 4140, 6900 }, HitEndTimes = new int[] { 2230, 4800, 7290 }, FileName = "AMCB0310.ani" } },
        { (3, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1920, 4860, 7940 }, HitEndTimes = new int[] { 4220, 6980, 9340 }, FileName = "AMCB0311.ani" } },
        { (3, 312, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1860, 4930 }, HitEndTimes = new int[] { 4290, 8770 }, FileName = "AMCB0312.ani" } },
        { (3, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5500 }, HitEndTimes = new int[] { 8700 }, FileName = "AMCB0313.ani" } },
        { (3, 314, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 6980 }, FileName = "AMCB0314.ani" } },
        { (3, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3843 }, HitEndTimes = new int[] { 5397 }, FileName = "AMCB0315.ani" } },
        { (3, 316, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2585, 3502 }, HitEndTimes = new int[] { 2926, 4420 }, FileName = "AMCB0316.ani" } },
        { (3, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4366 }, HitEndTimes = new int[] { 5238 }, FileName = "AMCB0317.ani" } },
        { (3, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3593 }, HitEndTimes = new int[] { 4708 }, FileName = "AMCB0318.ani" } },
        { (3, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4708 }, HitEndTimes = new int[] { 5920 }, FileName = "AMCB0319.ani" } },
        { (3, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4753 }, HitEndTimes = new int[] { 4996 }, FileName = "AMCB0320.ani" } },
        { (3, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5092 }, HitEndTimes = new int[] { 6063 }, FileName = "AMCB0326.ani" } },
        { (3, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4126 }, HitEndTimes = new int[] { 5433 }, FileName = "AMCB0327.ani" } },
        { (3, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4860 }, HitEndTimes = new int[] { 6160 }, FileName = "AMCB0330.ani" } },
        { (3, 331, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5530 }, HitEndTimes = new int[] { 6400 }, FileName = "AMCB0331.ani" } },
        { (3, 338, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2600, 4500 }, HitEndTimes = new int[] { 4500, 6400 }, FileName = "AMCB0338.ani" } },
        { (3, 339, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4800, 5340, 5880 }, HitEndTimes = new int[] { 5340, 5880, 6420 }, FileName = "AMCB0339.ani" } },
        { (3, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4500 }, HitEndTimes = new int[] { 4650 }, FileName = "AMCB0341.ani" } },
        { (3, 342, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1890, 5330 }, HitEndTimes = new int[] { 2040, 5480 }, FileName = "AMCB0342.ani" } },
        { (3, 406, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2150 }, HitEndTimes = new int[] { 2440 }, FileName = "AMCB0406.ani" } },
        { (3, 407, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1880 }, HitEndTimes = new int[] { 2080 }, FileName = "AMCB0407.ani" } },
        { (3, 408, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1390, 3070 }, HitEndTimes = new int[] { 1520, 3240 }, FileName = "AMCB0408.ani" } },
        { (3, 409, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2840 }, HitEndTimes = new int[] { 3010 }, FileName = "AMCB0409.ani" } },
        { (3, 500, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 630, 1850, 3140 }, HitEndTimes = new int[] { 790, 2350, 3400 }, FileName = "AMCB0500.ani" } },
        { (3, 501, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 860, 3140 }, HitEndTimes = new int[] { 1260, 3400 }, FileName = "AMCB0501.ani" } },
        { (3, 502, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 810, 3470 }, HitEndTimes = new int[] { 2280, 4880 }, FileName = "AMCB0502.ani" } },
        { (3, 503, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3340 }, HitEndTimes = new int[] { 4530 }, FileName = "AMCB0503.ani" } },
        { (3, 504, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1160, 3240 }, HitEndTimes = new int[] { 2770, 4660 }, FileName = "AMCB0504.ani" } },
        { (3, 505, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 760, 2770 }, HitEndTimes = new int[] { 2210, 4330 }, FileName = "AMCB0505.ani" } },
        { (3, 506, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 920, 3860 }, HitEndTimes = new int[] { 3200, 4790 }, FileName = "AMCB0506.ani" } },
        { (3, 507, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1060, 2150 }, HitEndTimes = new int[] { 1880, 4060 }, FileName = "AMCB0507.ani" } },
        { (3, 508, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 370, 2100 }, HitEndTimes = new int[] { 680, 2340 }, FileName = "AMCB0508.ani" } },
        { (3, 509, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 310, 1830 }, HitEndTimes = new int[] { 640, 2240 }, FileName = "AMCB0509.ani" } },
        { (3, 510, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 270, 1560, 3020 }, HitEndTimes = new int[] { 510, 1930, 3420 }, FileName = "AMCB0510.ani" } },
        { (3, 511, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 630, 3630 }, HitEndTimes = new int[] { 2380, 4560 }, FileName = "AMCB0511.ani" } },
        { (3, 512, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 430, 1980 }, HitEndTimes = new int[] { 1780, 4100 }, FileName = "AMCB0512.ani" } },
        { (3, 513, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 660, 2970 }, HitEndTimes = new int[] { 2080, 4620 }, FileName = "AMCB0513.ani" } },
        { (6, 209, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3630, 6740 }, HitEndTimes = new int[] { 4540, 7420 }, FileName = "AMCD0209.ani" } },
        { (6, 300, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3030, 3710, 5680 }, HitEndTimes = new int[] { 3560, 4390, 6510 }, FileName = "AMCD0300.ani" } },
        { (6, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1560 }, HitEndTimes = new int[] { 3510 }, FileName = "AMCD0301.ani" } },
        { (6, 302, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2270, 5310 }, HitEndTimes = new int[] { 2660, 5620 }, FileName = "AMCD0302.ani" } },
        { (6, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4610 }, HitEndTimes = new int[] { 4920 }, FileName = "AMCD0303.ani" } },
        { (6, 304, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1870 }, HitEndTimes = new int[] { 2110 }, FileName = "AMCD0304.ani" } },
        { (6, 305, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1670, 3710, 7190 }, HitEndTimes = new int[] { 2730, 5450, 8330 }, FileName = "AMCD0305.ani" } },
        { (6, 306, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6890 }, FileName = "AMCD0306.ani" } },
        { (6, 307, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3330, 4770, 7400 }, HitEndTimes = new int[] { 4320, 6590, 8400 }, FileName = "AMCD0307.ani" } },
        { (6, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3360, 4760, 7110 }, HitEndTimes = new int[] { 4220, 5620, 7970 }, FileName = "AMCD0311.ani" } },
        { (6, 312, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6290 }, FileName = "AMCD0312.ani" } },
        { (6, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6290 }, HitEndTimes = new int[] { 6970 }, FileName = "AMCD0313.ani" } },
        { (6, 314, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5690 }, HitEndTimes = new int[] { 6410 }, FileName = "AMCD0314.ani" } },
        { (6, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5070 }, HitEndTimes = new int[] { 6290 }, FileName = "AMCD0315.ani" } },
        { (6, 316, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6042 }, HitEndTimes = new int[] { 7141 }, FileName = "AMCD0316.ani" } },
        { (6, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5250 }, HitEndTimes = new int[] { 6080 }, FileName = "AMCD0317.ani" } },
        { (6, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3783 }, HitEndTimes = new int[] { 4465 }, FileName = "AMCD0318.ani" } },
        { (6, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "AMCD0319.ani" } },
        { (6, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5534 }, FileName = "AMCD0320.ani" } },
        { (6, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5180 }, HitEndTimes = new int[] { 6910 }, FileName = "AMCD0326.ani" } },
        { (6, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4564 }, HitEndTimes = new int[] { 5776 }, FileName = "AMCD0327.ani" } },
        { (6, 331, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5230 }, HitEndTimes = new int[] { 5830 }, FileName = "AMCD0331.ani" } },
        { (6, 339, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3120, 5650 }, HitEndTimes = new int[] { 4060, 7030 }, FileName = "AMCD0339.ani" } },
        { (6, 342, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2800, 4750 }, HitEndTimes = new int[] { 2950, 4900 }, FileName = "AMCD0342.ani" } },
        { (6, 409, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1880, 3600 }, HitEndTimes = new int[] { 2270, 4100 }, FileName = "AMCD0409.ani" } },
        { (6, 500, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1490, 2030, 3050 }, HitEndTimes = new int[] { 1880, 2230, 3240 }, FileName = "AMCD0500.ani" } },
        { (6, 501, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 770 }, HitEndTimes = new int[] { 1250 }, FileName = "AMCD0501.ani" } },
        { (6, 502, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1250 }, HitEndTimes = new int[] { 1450 }, FileName = "AMCD0502.ani" } },
        { (6, 503, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2060 }, HitEndTimes = new int[] { 2380 }, FileName = "AMCD0503.ani" } },
        { (6, 504, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1570 }, HitEndTimes = new int[] { 1980 }, FileName = "AMCD0504.ani" } },
        { (6, 505, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1310, 3200 }, HitEndTimes = new int[] { 2380, 3540 }, FileName = "AMCD0505.ani" } },
        { (6, 506, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2930 }, HitEndTimes = new int[] { 3530 }, FileName = "AMCD0506.ani" } },
        { (6, 507, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1130, 2580 }, HitEndTimes = new int[] { 1950, 3630 }, FileName = "AMCD0507.ani" } },
        { (6, 511, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1290, 1940, 3180 }, HitEndTimes = new int[] { 1610, 2300, 3790 }, FileName = "AMCD0511.ani" } },
        { (6, 512, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3090 }, HitEndTimes = new int[] { 3400 }, FileName = "AMCD0512.ani" } },
        { (6, 513, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2540 }, HitEndTimes = new int[] { 2890 }, FileName = "AMCD0513.ani" } },
        { (5, 206, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5480 }, HitEndTimes = new int[] { 10390 }, FileName = "AMCE0206.ani" } },
        { (5, 207, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 6110 }, FileName = "AMCE0207.ani" } },
        { (5, 208, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 9160, 11480, 13790 }, HitEndTimes = new int[] { 9900, 12630, 14210 }, FileName = "AMCE0208.ani" } },
        { (5, 209, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6410 }, HitEndTimes = new int[] { 7180 }, FileName = "AMCE0209.ani" } },
        { (5, 300, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5870 }, HitEndTimes = new int[] { 6770 }, FileName = "AMCE0300.ani" } },
        { (5, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6410 }, HitEndTimes = new int[] { 7180 }, FileName = "AMCE0301.ani" } },
        { (5, 302, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7000 }, HitEndTimes = new int[] { 8620 }, FileName = "AMCE0302.ani" } },
        { (5, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 8960 }, FileName = "AMCE0303.ani" } },
        { (5, 304, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2040, 4070, 5690 }, HitEndTimes = new int[] { 3530, 5270, 8320 }, FileName = "AMCE0304.ani" } },
        { (5, 305, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5950 }, HitEndTimes = new int[] { 8260 }, FileName = "AMCE0305.ani" } },
        { (5, 306, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7230 }, HitEndTimes = new int[] { 9150 }, FileName = "AMCE0306.ani" } },
        { (5, 307, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6270 }, HitEndTimes = new int[] { 8960 }, FileName = "AMCE0307.ani" } },
        { (5, 308, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2820, 4990, 7160 }, HitEndTimes = new int[] { 3150, 5520, 8010 }, FileName = "AMCE0308.ani" } },
        { (5, 309, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 850, 3880, 6570 }, HitEndTimes = new int[] { 1450, 4470, 7160 }, FileName = "AMCE0309.ani" } },
        { (5, 310, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3480, 5520, 7360 }, HitEndTimes = new int[] { 4140, 6370, 8280 }, FileName = "AMCE0310.ani" } },
        { (5, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3420, 5320, 6310 }, HitEndTimes = new int[] { 4990, 6040, 8010 }, FileName = "AMCE0311.ani" } },
        { (5, 312, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4540, 6340 }, HitEndTimes = new int[] { 5820, 9090 }, FileName = "AMCE0312.ani" } },
        { (5, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6440 }, HitEndTimes = new int[] { 8870 }, FileName = "AMCE0313.ani" } },
        { (5, 314, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 7940 }, FileName = "AMCE0314.ani" } },
        { (5, 315, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2699, 6087 }, HitEndTimes = new int[] { 3154, 6716 }, FileName = "AMCE0315.ani" } },
        { (5, 316, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3055 }, HitEndTimes = new int[] { 3347 }, FileName = "AMCE0316.ani" } },
        { (5, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3108 }, HitEndTimes = new int[] { 4852 }, FileName = "AMCE0317.ani" } },
        { (5, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4173 }, HitEndTimes = new int[] { 4660 }, FileName = "AMCE0318.ani" } },
        { (5, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4950 }, HitEndTimes = new int[] { 5140 }, FileName = "AMCE0319.ani" } },
        { (5, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4609 }, HitEndTimes = new int[] { 4897 }, FileName = "AMCE0320.ani" } },
        { (5, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5966 }, HitEndTimes = new int[] { 6451 }, FileName = "AMCE0326.ani" } },
        { (5, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4514 }, HitEndTimes = new int[] { 5389 }, FileName = "AMCE0327.ani" } },
        { (5, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4120 }, HitEndTimes = new int[] { 5720 }, FileName = "AMCE0330.ani" } },
        { (5, 331, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2090 }, HitEndTimes = new int[] { 6110 }, FileName = "AMCE0331.ani" } },
        { (5, 338, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2630 }, HitEndTimes = new int[] { 7120 }, FileName = "AMCE0338.ani" } },
        { (5, 339, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2220, 5000 }, HitEndTimes = new int[] { 3800, 5850 }, FileName = "AMCE0339.ani" } },
        { (5, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5850 }, HitEndTimes = new int[] { 6000 }, FileName = "AMCE0341.ani" } },
        { (5, 342, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4800, 4950, 5100 }, HitEndTimes = new int[] { 4950, 5100, 5250 }, FileName = "AMCE0342.ani" } },
        { (5, 401, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4180 }, HitEndTimes = new int[] { 5400 }, FileName = "AMCE0401.ani" } },
        { (1, 206, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4770 }, HitEndTimes = new int[] { 5540 }, FileName = "AMCK0206.ani" } },
        { (1, 207, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6950 }, HitEndTimes = new int[] { 7600 }, FileName = "AMCK0207.ani" } },
        { (1, 208, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2810, 5270 }, HitEndTimes = new int[] { 3170, 5570 }, FileName = "AMCK0208.ani" } },
        { (1, 209, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7940 }, HitEndTimes = new int[] { 8770 }, FileName = "AMCK0209.ani" } },
        { (1, 300, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1910, 4340, 6240 }, HitEndTimes = new int[] { 2230, 4660, 6640 }, FileName = "AMCK0300.ani" } },
        { (1, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5750 }, HitEndTimes = new int[] { 6470 }, FileName = "AMCK0301.ani" } },
        { (1, 302, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1660, 4160, 7740 }, HitEndTimes = new int[] { 2820, 5570, 9150 }, FileName = "AMCK0302.ani" } },
        { (1, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8060 }, HitEndTimes = new int[] { 9540 }, FileName = "AMCK0303.ani" } },
        { (1, 304, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3970 }, HitEndTimes = new int[] { 7300 }, FileName = "AMCK0304.ani" } },
        { (1, 305, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4030, 5310, 7100 }, HitEndTimes = new int[] { 5120, 6850, 9280 }, FileName = "AMCK0305.ani" } },
        { (1, 306, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1540, 4420, 8260 }, HitEndTimes = new int[] { 2820, 5700, 9090 }, FileName = "AMCK0306.ani" } },
        { (1, 307, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7550 }, HitEndTimes = new int[] { 9220 }, FileName = "AMCK0307.ani" } },
        { (1, 308, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4460 }, HitEndTimes = new int[] { 5400 }, FileName = "AMCK0308.ani" } },
        { (1, 309, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4270, 7820 }, HitEndTimes = new int[] { 4730, 8280 }, FileName = "AMCK0309.ani" } },
        { (1, 310, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4660 }, HitEndTimes = new int[] { 5650 }, FileName = "AMCK0310.ani" } },
        { (1, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1340, 5890, 7360 }, HitEndTimes = new int[] { 4290, 6910, 9150 }, FileName = "AMCK0311.ani" } },
        { (1, 312, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3650, 6460 }, HitEndTimes = new int[] { 6020, 8960 }, FileName = "AMCK0312.ani" } },
        { (1, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 8830 }, FileName = "AMCK0313.ani" } },
        { (1, 314, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6720 }, HitEndTimes = new int[] { 7040 }, FileName = "AMCK0314.ani" } },
        { (1, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1668 }, HitEndTimes = new int[] { 2009 }, FileName = "AMCK0315.ani" } },
        { (1, 316, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1433, 2987 }, HitEndTimes = new int[] { 2183, 3617 }, FileName = "AMCK0316.ani" } },
        { (1, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3684 }, HitEndTimes = new int[] { 4852 }, FileName = "AMCK0317.ani" } },
        { (1, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3836 }, HitEndTimes = new int[] { 4852 }, FileName = "AMCK0318.ani" } },
        { (1, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4897 }, HitEndTimes = new int[] { 5049 }, FileName = "AMCK0319.ani" } },
        { (1, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4708 }, HitEndTimes = new int[] { 4996 }, FileName = "AMCK0320.ani" } },
        { (1, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3836 }, HitEndTimes = new int[] { 4222 }, FileName = "AMCK0326.ani" } },
        { (1, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5243 }, HitEndTimes = new int[] { 6110 }, FileName = "AMCK0327.ani" } },
        { (1, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4900 }, HitEndTimes = new int[] { 4990 }, FileName = "AMCK0330.ani" } },
        { (1, 331, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1540, 2740, 4900 }, HitEndTimes = new int[] { 2180, 3090, 6900 }, FileName = "AMCK0331.ani" } },
        { (1, 338, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4490 }, HitEndTimes = new int[] { 6000 }, FileName = "AMCK0338.ani" } },
        { (1, 339, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5000, 5500, 6000 }, HitEndTimes = new int[] { 5500, 6000, 6500 }, FileName = "AMCK0339.ani" } },
        { (1, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5330 }, HitEndTimes = new int[] { 5480 }, FileName = "AMCK0341.ani" } },
        { (1, 342, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4730, 6460 }, HitEndTimes = new int[] { 4880, 6610 }, FileName = "AMCK0342.ani" } },
        { (1, 406, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3110 }, HitEndTimes = new int[] { 3570 }, FileName = "AMCK0406.ani" } },
        { (1, 407, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3140 }, HitEndTimes = new int[] { 3530 }, FileName = "AMCK0407.ani" } },
        { (1, 408, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 790, 2710 }, HitEndTimes = new int[] { 1060, 3270 }, FileName = "AMCK0408.ani" } },
        { (1, 409, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3960 }, HitEndTimes = new int[] { 4200 }, FileName = "AMCK0409.ani" } },
        { (1, 500, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1420, 3140 }, HitEndTimes = new int[] { 1720, 3400 }, FileName = "AMCK0500.ani" } },
        { (1, 501, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2110 }, HitEndTimes = new int[] { 3140 }, FileName = "AMCK0501.ani" } },
        { (1, 502, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 790, 2110, 3860 }, HitEndTimes = new int[] { 1650, 3010, 4620 }, FileName = "AMCK0502.ani" } },
        { (1, 503, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4120 }, HitEndTimes = new int[] { 5240 }, FileName = "AMCK0503.ani" } },
        { (1, 504, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2110 }, HitEndTimes = new int[] { 4100 }, FileName = "AMCK0504.ani" } },
        { (1, 505, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1980, 2770, 3770 }, HitEndTimes = new int[] { 2510, 3370, 4760 }, FileName = "AMCK0505.ani" } },
        { (1, 506, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1450, 3670 }, HitEndTimes = new int[] { 2110, 4720 }, FileName = "AMCK0506.ani" } },
        { (1, 507, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2810 }, HitEndTimes = new int[] { 4620 }, FileName = "AMCK0507.ani" } },
        { (1, 508, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1800 }, HitEndTimes = new int[] { 2410 }, FileName = "AMCK0508.ani" } },
        { (1, 509, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3050 }, HitEndTimes = new int[] { 3590 }, FileName = "AMCK0509.ani" } },
        { (1, 510, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2100 }, HitEndTimes = new int[] { 2750 }, FileName = "AMCK0510.ani" } },
        { (1, 511, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 790, 2410, 3140 }, HitEndTimes = new int[] { 1450, 2870, 4390 }, FileName = "AMCK0511.ani" } },
        { (1, 512, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1120, 2610 }, HitEndTimes = new int[] { 2310, 4390 }, FileName = "AMCK0512.ani" } },
        { (1, 513, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1980 }, HitEndTimes = new int[] { 4100 }, FileName = "AMCK0513.ani" } },
        { (7, 206, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "AMCM0206.ani" } },
        { (7, 207, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "AMCM0207.ani" } },
        { (7, 208, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "AMCM0208.ani" } },
        { (7, 209, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "AMCM0209.ani" } },
        { (7, 300, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "AMCM0300.ani" } },
        { (7, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "AMCM0301.ani" } },
        { (7, 302, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "AMCM0302.ani" } },
        { (7, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "AMCM0303.ani" } },
        { (7, 304, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "AMCM0304.ani" } },
        { (7, 305, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "AMCM0305.ani" } },
        { (7, 306, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "AMCM0306.ani" } },
        { (7, 307, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "AMCM0307.ani" } },
        { (7, 308, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6980 }, HitEndTimes = new int[] { 7170 }, FileName = "AMCM0308.ani" } },
        { (7, 309, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6980 }, HitEndTimes = new int[] { 7170 }, FileName = "AMCM0309.ani" } },
        { (7, 310, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6980 }, HitEndTimes = new int[] { 7170 }, FileName = "AMCM0310.ani" } },
        { (7, 311, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5310 }, HitEndTimes = new int[] { 5630 }, FileName = "AMCM0311.ani" } },
        { (7, 312, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5310 }, HitEndTimes = new int[] { 5630 }, FileName = "AMCM0312.ani" } },
        { (7, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4740 }, HitEndTimes = new int[] { 4860 }, FileName = "AMCM0313.ani" } },
        { (7, 314, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4740 }, HitEndTimes = new int[] { 4860 }, FileName = "AMCM0314.ani" } },
        { (7, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5723 }, FileName = "AMCM0315.ani" } },
        { (7, 316, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5723 }, FileName = "AMCM0316.ani" } },
        { (7, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5435 }, FileName = "AMCM0317.ani" } },
        { (7, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5435 }, FileName = "AMCM0318.ani" } },
        { (7, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5678 }, HitEndTimes = new int[] { 5920 }, FileName = "AMCM0319.ani" } },
        { (7, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5678 }, HitEndTimes = new int[] { 5920 }, FileName = "AMCM0320.ani" } },
        { (7, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4122 }, HitEndTimes = new int[] { 5333 }, FileName = "AMCM0326.ani" } },
        { (7, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4852 }, HitEndTimes = new int[] { 5625 }, FileName = "AMCM0327.ani" } },
        { (7, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5370 }, HitEndTimes = new int[] { 6580 }, FileName = "AMCM0330.ani" } },
        { (7, 331, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5550 }, HitEndTimes = new int[] { 6420 }, FileName = "AMCM0331.ani" } },
        { (7, 338, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5300 }, HitEndTimes = new int[] { 6270 }, FileName = "AMCM0338.ani" } },
        { (7, 339, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1000, 3600 }, HitEndTimes = new int[] { 3600, 6200 }, FileName = "AMCM0339.ani" } },
        { (7, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5130 }, HitEndTimes = new int[] { 5280 }, FileName = "AMCM0341.ani" } },
        { (7, 342, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2100, 3200, 5600 }, HitEndTimes = new int[] { 2250, 3350, 5750 }, FileName = "AMCM0342.ani" } },
        { (7, 401, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4180 }, HitEndTimes = new int[] { 5400 }, FileName = "AMCM0401.ani" } },
        { (2, 206, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6350 }, HitEndTimes = new int[] { 7420 }, FileName = "AMCS0206.ani" } },
        { (2, 207, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5750 }, HitEndTimes = new int[] { 6710 }, FileName = "AMCS0207.ani" } },
        { (2, 208, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6710 }, HitEndTimes = new int[] { 7660 }, FileName = "AMCS0208.ani" } },
        { (2, 209, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5250, 6200 }, HitEndTimes = new int[] { 5790, 7020 }, FileName = "AMCS0209.ani" } },
        { (2, 300, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8060 }, HitEndTimes = new int[] { 8790 }, FileName = "AMCS0300.ani" } },
        { (2, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6470 }, HitEndTimes = new int[] { 7120 }, FileName = "AMCS0301.ani" } },
        { (2, 302, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4100, 5250, 7170 }, HitEndTimes = new int[] { 5120, 6850, 9340 }, FileName = "AMCS0302.ani" } },
        { (2, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7300 }, HitEndTimes = new int[] { 8770 }, FileName = "AMCS0303.ani" } },
        { (2, 304, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7300 }, HitEndTimes = new int[] { 9150 }, FileName = "AMCS0304.ani" } },
        { (2, 305, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2240, 5310, 7620 }, HitEndTimes = new int[] { 3970, 7040, 9150 }, FileName = "AMCS0305.ani" } },
        { (2, 306, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1540, 2880, 7620 }, HitEndTimes = new int[] { 2370, 4990, 9220 }, FileName = "AMCS0306.ani" } },
        { (2, 307, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2510, 4670, 6770 }, HitEndTimes = new int[] { 4070, 6170, 8200 }, FileName = "AMCS0307.ani" } },
        { (2, 308, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1820, 3860 }, HitEndTimes = new int[] { 2420, 4300 }, FileName = "AMCS0308.ani" } },
        { (2, 309, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 460, 3280, 5980 }, HitEndTimes = new int[] { 1050, 3680, 6310 }, FileName = "AMCS0309.ani" } },
        { (2, 310, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5450 }, HitEndTimes = new int[] { 6040 }, FileName = "AMCS0310.ani" } },
        { (2, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1540, 4420, 7870 }, HitEndTimes = new int[] { 3460, 6340, 9280 }, FileName = "AMCS0311.ani" } },
        { (2, 312, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3840, 7420 }, HitEndTimes = new int[] { 6660, 9280 }, FileName = "AMCS0312.ani" } },
        { (2, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7170 }, HitEndTimes = new int[] { 8770 }, FileName = "AMCS0313.ani" } },
        { (2, 314, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3650, 4800, 6530 }, HitEndTimes = new int[] { 3900, 4990, 7040 }, FileName = "AMCS0314.ani" } },
        { (2, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5049 }, HitEndTimes = new int[] { 5572 }, FileName = "AMCS0315.ani" } },
        { (2, 316, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2812, 4480 }, HitEndTimes = new int[] { 3275, 4768 }, FileName = "AMCS0316.ani" } },
        { (2, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4510 }, HitEndTimes = new int[] { 5776 }, FileName = "AMCS0317.ani" } },
        { (2, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3154 }, HitEndTimes = new int[] { 4806 }, FileName = "AMCS0318.ani" } },
        { (2, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4609 }, HitEndTimes = new int[] { 5049 }, FileName = "AMCS0319.ani" } },
        { (2, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3442 }, HitEndTimes = new int[] { 3639 }, FileName = "AMCS0320.ani" } },
        { (2, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3976 }, HitEndTimes = new int[] { 5292 }, FileName = "AMCS0326.ani" } },
        { (2, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3688 }, HitEndTimes = new int[] { 5143 }, FileName = "AMCS0327.ani" } },
        { (2, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3010 }, HitEndTimes = new int[] { 5090 }, FileName = "AMCS0330.ani" } },
        { (2, 331, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2630, 3760, 5160 }, HitEndTimes = new int[] { 3060, 4090, 5790 }, FileName = "AMCS0331.ani" } },
        { (2, 338, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5080 }, HitEndTimes = new int[] { 6840 }, FileName = "AMCS0338.ani" } },
        { (2, 339, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2930, 4720 }, HitEndTimes = new int[] { 3550, 6730 }, FileName = "AMCS0339.ani" } },
        { (2, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 5910 }, FileName = "AMCS0341.ani" } },
        { (2, 342, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 6240, 6390 }, HitEndTimes = new int[] { 6390, 6540 }, FileName = "AMCS0342.ani" } },
        { (2, 406, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3470 }, HitEndTimes = new int[] { 4130 }, FileName = "AMCS0406.ani" } },
        { (2, 407, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2910 }, HitEndTimes = new int[] { 3470 }, FileName = "AMCS0407.ani" } },
        { (2, 408, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3470 }, HitEndTimes = new int[] { 3730 }, FileName = "AMCS0408.ani" } },
        { (2, 409, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2770, 3730 }, HitEndTimes = new int[] { 2970, 4160 }, FileName = "AMCS0409.ani" } },
        { (2, 500, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2010 }, HitEndTimes = new int[] { 2410 }, FileName = "AMCS0500.ani" } },
        { (2, 501, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3770 }, HitEndTimes = new int[] { 4130 }, FileName = "AMCS0501.ani" } },
        { (2, 502, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1190, 2380, 3640 }, HitEndTimes = new int[] { 2160, 3270, 5350 }, FileName = "AMCS0502.ani" } },
        { (2, 503, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3440 }, HitEndTimes = new int[] { 5020 }, FileName = "AMCS0503.ani" } },
        { (2, 504, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3190 }, HitEndTimes = new int[] { 5050 }, FileName = "AMCS0504.ani" } },
        { (2, 505, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 660, 2010, 3140 }, HitEndTimes = new int[] { 1650, 2840, 4530 }, FileName = "AMCS0505.ani" } },
        { (2, 506, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 560, 2610 }, HitEndTimes = new int[] { 1680, 4530 }, FileName = "AMCS0506.ani" } },
        { (2, 507, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1390, 2680, 3700 }, HitEndTimes = new int[] { 2210, 3400, 4720 }, FileName = "AMCS0507.ani" } },
        { (2, 508, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 1760 }, HitEndTimes = new int[] { 810, 2140 }, FileName = "AMCS0508.ani" } },
        { (2, 509, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 30, 1830 }, HitEndTimes = new int[] { 610, 2510 }, FileName = "AMCS0509.ani" } },
        { (2, 510, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1050 }, HitEndTimes = new int[] { 1530 }, FileName = "AMCS0510.ani" } },
        { (2, 511, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 30, 1660, 3700 }, HitEndTimes = new int[] { 540, 2680, 4100 }, FileName = "AMCS0511.ani" } },
        { (2, 512, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1850, 4060 }, HitEndTimes = new int[] { 3400, 4790 }, FileName = "AMCS0512.ani" } },
        { (2, 513, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2350 }, HitEndTimes = new int[] { 4260 }, FileName = "AMCS0513.ani" } },
        { (8, 307, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 7200, 7900, 8590 }, HitEndTimes = new int[] { 7580, 8210, 8900 }, FileName = "AMCV0307.ani" } },
        { (8, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4512 }, HitEndTimes = new int[] { 4850 }, FileName = "AMCV0326.ani" } },
        { (8, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7200 }, HitEndTimes = new int[] { 7740 }, FileName = "AMCV0326_1.ani" } },
        { (8, 327, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4045, 4465, 4898, 5290, 5724 }, HitEndTimes = new int[] { 4272, 4658, 5092, 5483, 5879 }, FileName = "AMCV0327.ani" } },
        { (8, 327, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 7746, 8550, 9380, 10130, 10960 }, HitEndTimes = new int[] { 8180, 8920, 9750, 10500, 11240 }, FileName = "AMCV0327_1.ani" } },
        { (8, 328, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4850 }, HitEndTimes = new int[] { 5095 }, FileName = "AMCV0328.ani" } },
        { (8, 328, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7740 }, HitEndTimes = new int[] { 8130 }, FileName = "AMCV0328_1.ani" } },
        { (10, 203, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1790, 3820 }, HitEndTimes = new int[] { 1890, 3920 }, FileName = "AMCZ0203.ani" } },
        { (10, 204, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2020 }, HitEndTimes = new int[] { 2120 }, FileName = "AMCZ0204.ani" } },
        { (10, 205, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1930 }, HitEndTimes = new int[] { 2030 }, FileName = "AMCZ0205.ani" } },
        { (10, 206, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2220, 2690, 3950, 5210 }, HitEndTimes = new int[] { 2270, 2740, 4000, 5260 }, FileName = "AMCZ0206.ani" } },
        { (10, 207, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2220, 2690, 3950, 5210, 6300 }, HitEndTimes = new int[] { 2270, 2740, 4000, 5260, 6350 }, FileName = "AMCZ0207.ani" } },
        { (10, 208, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2590, 3000, 5280, 6520, 8440 }, HitEndTimes = new int[] { 2690, 3100, 5300, 6620, 8540 }, FileName = "AMCZ0208.ani" } },
        { (10, 209, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3760 }, HitEndTimes = new int[] { 3860 }, FileName = "AMCZ0209.ani" } },
        { (10, 210, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4210 }, HitEndTimes = new int[] { 5500 }, FileName = "AMCZ0210.ani" } },
        { (10, 211, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 910, 2670, 4280, 7160 }, HitEndTimes = new int[] { 1010, 2770, 4380, 7260 }, FileName = "AMCZ0211.ani" } },
        { (10, 213, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3670 }, HitEndTimes = new int[] { 3770 }, FileName = "AMCZ0213.ani" } },
        { (10, 214, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 950, 2650, 4250, 7230 }, HitEndTimes = new int[] { 1050, 2750, 4350, 7330 }, FileName = "AMCZ0214.ani" } },
        { (10, 215, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 810, 2090, 4240 }, HitEndTimes = new int[] { 860, 2140, 4340 }, FileName = "AMCZ0215.ani" } },
        { (10, 217, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1860, 3900 }, HitEndTimes = new int[] { 1960, 4000 }, FileName = "AMCZ0217.ani" } },
        { (10, 218, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1340, 3860 }, HitEndTimes = new int[] { 1440, 3960 }, FileName = "AMCZ0218.ani" } },
        { (10, 219, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1240, 2560 }, HitEndTimes = new int[] { 1340, 2660 }, FileName = "AMCZ0219.ani" } },
        { (10, 220, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1700, 3830 }, HitEndTimes = new int[] { 1800, 3930 }, FileName = "AMCZ0220.ani" } },
        { (10, 221, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1670, 4350 }, HitEndTimes = new int[] { 1770, 4450 }, FileName = "AMCZ0221.ani" } },
        { (10, 222, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2430, 6270 }, HitEndTimes = new int[] { 2530, 6370 }, FileName = "AMCZ0222.ani" } },
        { (10, 223, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 3060 }, HitEndTimes = new int[] { 1760, 3160 }, FileName = "AMCZ0223.ani" } },
        { (10, 300, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1750, 4200 }, HitEndTimes = new int[] { 1850, 4300 }, FileName = "AMCZ0300.ani" } },
        { (10, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4000 }, HitEndTimes = new int[] { 4100 }, FileName = "AMCZ0301.ani" } },
        { (10, 302, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 710, 1570, 2280, 2920, 6130 }, HitEndTimes = new int[] { 810, 1670, 2380, 3020, 6230 }, FileName = "AMCZ0302.ani" } },
        { (10, 303, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 1150, 2430, 3970, 6500 }, HitEndTimes = new int[] { 1250, 2530, 4070, 6600 }, FileName = "AMCZ0303.ani" } },
        { (10, 304, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1770, 3450 }, HitEndTimes = new int[] { 1870, 3550 }, FileName = "AMCZ0304.ani" } },
        { (10, 305, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2430, 4230, 5420 }, HitEndTimes = new int[] { 2530, 4330, 5520 }, FileName = "AMCZ0305.ani" } },
        { (10, 306, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1700, 2850, 4450 }, HitEndTimes = new int[] { 1800, 2950, 4550 }, FileName = "AMCZ0306.ani" } },
        { (10, 307, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 500, 1900, 5570 }, HitEndTimes = new int[] { 600, 2000, 5670 }, FileName = "AMCZ0307.ani" } },
        { (10, 308, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2630, 4130, 6170, 7720 }, HitEndTimes = new int[] { 2730, 4230, 6270, 7820 }, FileName = "AMCZ0308.ani" } },
        { (10, 309, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2630, 4130, 6170, 7720, 10840 }, HitEndTimes = new int[] { 2730, 4230, 6270, 7820, 11840 }, FileName = "AMCZ0309.ani" } },
        { (10, 310, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1300, 4400 }, HitEndTimes = new int[] { 1400, 4500 }, FileName = "AMCZ0310.ani" } },
        { (10, 311, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2570, 3230, 3890, 4490 }, HitEndTimes = new int[] { 2620, 3280, 3940, 4540 }, FileName = "AMCZ0311.ani" } },
        { (10, 312, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2080, 2430, 2890, 3240, 3930 }, HitEndTimes = new int[] { 2180, 2530, 2990, 3340, 4030 }, FileName = "AMCZ0312.ani" } },
        { (10, 313, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1250, 4150 }, HitEndTimes = new int[] { 1350, 4250 }, FileName = "AMCZ0313.ani" } },
        { (10, 314, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3700 }, HitEndTimes = new int[] { 3800 }, FileName = "AMCZ0314.ani" } },
        { (10, 315, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 1211, 2186, 3397, 4999 }, HitEndTimes = new int[] { 1282, 2257, 3468, 5070 }, FileName = "AMCZ0315.ani" } },
        { (10, 316, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4759 }, HitEndTimes = new int[] { 4843 }, FileName = "AMCZ0316.ani" } },
        { (10, 317, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 844, 1687, 2428, 3055, 4320 }, HitEndTimes = new int[] { 904, 1747, 2489, 3115, 4381 }, FileName = "AMCZ0317.ani" } },
        { (10, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3878 }, HitEndTimes = new int[] { 3961 }, FileName = "AMCZ0318.ani" } },
        { (10, 319, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 586, 1564, 2466, 3930 }, HitEndTimes = new int[] { 663, 1641, 2543, 4007 }, FileName = "AMCZ0319.ani" } },
        { (10, 320, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 924, 1945, 2553, 3104, 5147 }, HitEndTimes = new int[] { 1005, 2026, 2634, 3185, 5228 }, FileName = "AMCZ0320.ani" } },
        { (10, 326, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1330, 4080 }, HitEndTimes = new int[] { 1430, 4180 }, FileName = "AMCZ0326.ani" } },
        { (10, 327, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2444, 4183 }, HitEndTimes = new int[] { 2538, 4277 }, FileName = "AMCZ0327.ani" } },
        { (10, 330, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2830, 5550 }, HitEndTimes = new int[] { 3060, 6000 }, FileName = "AMCZ0330.ani" } },
        { (10, 331, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3350, 4550 }, HitEndTimes = new int[] { 3540, 5320 }, FileName = "AMCZ0331.ani" } },
        { (10, 338, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3880, 5450, 6530 }, HitEndTimes = new int[] { 4500, 5740, 6870 }, FileName = "AMCZ0338.ani" } },
        { (10, 339, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2280, 3350, 5430 }, HitEndTimes = new int[] { 3010, 4460, 6580 }, FileName = "AMCZ0339.ani" } },
        { (10, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5350 }, HitEndTimes = new int[] { 5500 }, FileName = "AMCZ0341.ani" } },
        { (10, 342, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3200, 3850, 4960 }, HitEndTimes = new int[] { 3350, 4000, 5110 }, FileName = "AMCZ0342.ani" } },
        { (4, 206, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 990 }, HitEndTimes = new int[] { 6110 }, FileName = "BMCA0206.ani" } },
        { (4, 207, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2230, 6770 }, HitEndTimes = new int[] { 3280, 7160 }, FileName = "BMCA0207.ani" } },
        { (4, 208, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3020 }, HitEndTimes = new int[] { 5910 }, FileName = "BMCA0208.ani" } },
        { (4, 209, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2820 }, HitEndTimes = new int[] { 4860 }, FileName = "BMCA0209.ani" } },
        { (4, 300, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 590, 6310 }, HitEndTimes = new int[] { 2170, 7030 }, FileName = "BMCA0300.ani" } },
        { (4, 301, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3550, 6770 }, HitEndTimes = new int[] { 4660, 7100 }, FileName = "BMCA0301.ani" } },
        { (4, 302, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4360 }, HitEndTimes = new int[] { 5590 }, FileName = "BMCA0302.ani" } },
        { (4, 303, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1510, 4470 }, HitEndTimes = new int[] { 2230, 5120 }, FileName = "BMCA0303.ani" } },
        { (4, 304, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5320 }, HitEndTimes = new int[] { 6770 }, FileName = "BMCA0304.ani" } },
        { (4, 305, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2300 }, HitEndTimes = new int[] { 5520 }, FileName = "BMCA0305.ani" } },
        { (4, 306, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3280 }, HitEndTimes = new int[] { 6040 }, FileName = "BMCA0306.ani" } },
        { (4, 307, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1640, 3220, 6040 }, HitEndTimes = new int[] { 2230, 4200, 7030 }, FileName = "BMCA0307.ani" } },
        { (4, 308, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3020, 6960 }, HitEndTimes = new int[] { 4730, 8470 }, FileName = "BMCA0308.ani" } },
        { (4, 309, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1250, 5720, 7620 }, HitEndTimes = new int[] { 1970, 6700, 8740 }, FileName = "BMCA0309.ani" } },
        { (4, 310, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 330, 3350, 6770 }, HitEndTimes = new int[] { 1770, 4870, 8210 }, FileName = "BMCA0310.ani" } },
        { (4, 311, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3010, 6720 }, HitEndTimes = new int[] { 4030, 7620 }, FileName = "BMCA0311.ani" } },
        { (4, 312, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2880, 6910 }, HitEndTimes = new int[] { 4420, 7870 }, FileName = "BMCA0312.ani" } },
        { (4, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5380 }, HitEndTimes = new int[] { 7680 }, FileName = "BMCA0313.ani" } },
        { (4, 314, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7040 }, HitEndTimes = new int[] { 8190 }, FileName = "BMCA0314.ani" } },
        { (4, 315, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5223, 6254 }, HitEndTimes = new int[] { 5397, 6603 }, FileName = "BMCA0315.ani" } },
        { (4, 316, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 6890, 8250 }, HitEndTimes = new int[] { 7120, 8710 }, FileName = "BMCA0316.ani" } },
        { (4, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1530 }, HitEndTimes = new int[] { 5340 }, FileName = "BMCA0317.ani" } },
        { (4, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3010 }, HitEndTimes = new int[] { 3154 }, FileName = "BMCA0318.ani" } },
        { (4, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4897 }, HitEndTimes = new int[] { 5382 }, FileName = "BMCA0319.ani" } },
        { (4, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5721 }, HitEndTimes = new int[] { 6110 }, FileName = "BMCA0326.ani" } },
        { (4, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6020 }, HitEndTimes = new int[] { 6590 }, FileName = "BMCA0327.ani" } },
        { (4, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3150 }, HitEndTimes = new int[] { 5430 }, FileName = "BMCA0330.ani" } },
        { (4, 338, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2230 }, HitEndTimes = new int[] { 5870 }, FileName = "BMCA0338.ani" } },
        { (4, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4330 }, HitEndTimes = new int[] { 4510 }, FileName = "BMCA0341.ani" } },
        { (4, 406, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 30 }, HitEndTimes = new int[] { 2610 }, FileName = "BMCA0406.ani" } },
        { (4, 407, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1630 }, HitEndTimes = new int[] { 2780 }, FileName = "BMCA0407.ani" } },
        { (4, 408, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 950 }, HitEndTimes = new int[] { 2780 }, FileName = "BMCA0408.ani" } },
        { (4, 409, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1120 }, HitEndTimes = new int[] { 2140 }, FileName = "BMCA0409.ani" } },
        { (4, 500, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 540 }, HitEndTimes = new int[] { 2240 }, FileName = "BMCA0500.ani" } },
        { (4, 501, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 610 }, HitEndTimes = new int[] { 1900 }, FileName = "BMCA0501.ani" } },
        { (4, 502, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 410 }, HitEndTimes = new int[] { 1900 }, FileName = "BMCA0502.ani" } },
        { (4, 503, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1420 }, HitEndTimes = new int[] { 1930 }, FileName = "BMCA0503.ani" } },
        { (4, 504, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2370 }, HitEndTimes = new int[] { 3390 }, FileName = "BMCA0504.ani" } },
        { (4, 505, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 540 }, HitEndTimes = new int[] { 2270 }, FileName = "BMCA0505.ani" } },
        { (4, 506, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 510 }, HitEndTimes = new int[] { 2030 }, FileName = "BMCA0506.ani" } },
        { (4, 507, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 640, 1560, 2510 }, HitEndTimes = new int[] { 1150, 2000, 3050 }, FileName = "BMCA0507.ani" } },
        { (4, 508, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2340 }, HitEndTimes = new int[] { 3630 }, FileName = "BMCA0508.ani" } },
        { (4, 509, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2240, 3560 }, HitEndTimes = new int[] { 2880, 3930 }, FileName = "BMCA0509.ani" } },
        { (4, 510, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 370, 1930 }, HitEndTimes = new int[] { 850, 3660 }, FileName = "BMCA0510.ani" } },
        { (4, 511, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 200, 2280 }, HitEndTimes = new int[] { 1020, 3040 }, FileName = "BMCA0511.ani" } },
        { (4, 512, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1820 }, HitEndTimes = new int[] { 3370 }, FileName = "BMCA0512.ani" } },
        { (4, 513, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2250 }, HitEndTimes = new int[] { 3370 }, FileName = "BMCA0513.ani" } },
        { (3, 206, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6950 }, FileName = "BMCB0206.ani" } },
        { (3, 207, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5390 }, HitEndTimes = new int[] { 6530 }, FileName = "BMCB0207.ani" } },
        { (3, 208, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5750 }, HitEndTimes = new int[] { 6530 }, FileName = "BMCB0208.ani" } },
        { (3, 209, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3470, 5690 }, HitEndTimes = new int[] { 4430, 6170 }, FileName = "BMCB0209.ani" } },
        { (3, 300, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 6410, 7180, 7960 }, HitEndTimes = new int[] { 6830, 7540, 8380 }, FileName = "BMCB0300.ani" } },
        { (3, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6590 }, HitEndTimes = new int[] { 7240 }, FileName = "BMCB0301.ani" } },
        { (3, 302, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 4350, 6980 }, HitEndTimes = new int[] { 3460, 6270, 9470 }, FileName = "BMCB0302.ani" } },
        { (3, 303, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4740, 6660 }, HitEndTimes = new int[] { 5950, 8900 }, FileName = "BMCB0303.ani" } },
        { (3, 304, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5210, 6170 }, HitEndTimes = new int[] { 5990, 8560 }, FileName = "BMCB0304.ani" } },
        { (3, 305, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3460, 5630, 6780 }, HitEndTimes = new int[] { 4930, 6460, 8960 }, FileName = "BMCB0305.ani" } },
        { (3, 306, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1980, 5570, 7810 }, HitEndTimes = new int[] { 4030, 7420, 9410 }, FileName = "BMCB0306.ani" } },
        { (3, 307, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3010, 6020, 8900 }, HitEndTimes = new int[] { 4540, 7360, 9540 }, FileName = "BMCB0307.ani" } },
        { (3, 308, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3190, 5490 }, HitEndTimes = new int[] { 3730, 6040 }, FileName = "BMCB0308.ani" } },
        { (3, 309, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2920, 7050 }, HitEndTimes = new int[] { 3800, 7530 }, FileName = "BMCB0309.ani" } },
        { (3, 310, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1700, 6920 }, HitEndTimes = new int[] { 2310, 7660 }, FileName = "BMCB0310.ani" } },
        { (3, 311, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1600, 4420, 7100 }, HitEndTimes = new int[] { 3460, 5890, 8830 }, FileName = "BMCB0311.ani" } },
        { (3, 312, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1980, 5120 }, HitEndTimes = new int[] { 4290, 8260 }, FileName = "BMCB0312.ani" } },
        { (3, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 8770 }, FileName = "BMCB0313.ani" } },
        { (3, 314, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 6980 }, FileName = "BMCB0314.ani" } },
        { (3, 315, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4420 }, HitEndTimes = new int[] { 4882 }, FileName = "BMCB0315.ani" } },
        { (3, 316, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2411, 5852 }, HitEndTimes = new int[] { 2638, 6201 }, FileName = "BMCB0316.ani" } },
        { (3, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2812 }, HitEndTimes = new int[] { 3881 }, FileName = "BMCB0317.ani" } },
        { (3, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2229 }, HitEndTimes = new int[] { 3836 }, FileName = "BMCB0318.ani" } },
        { (3, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4708 }, HitEndTimes = new int[] { 5094 }, FileName = "BMCB0319.ani" } },
        { (3, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4996 }, HitEndTimes = new int[] { 5382 }, FileName = "BMCB0320.ani" } },
        { (3, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5092 }, HitEndTimes = new int[] { 6063 }, FileName = "BMCB0326.ani" } },
        { (3, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4126 }, HitEndTimes = new int[] { 5433 }, FileName = "BMCB0327.ani" } },
        { (3, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4860 }, HitEndTimes = new int[] { 6160 }, FileName = "BMCB0330.ani" } },
        { (3, 331, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5530 }, HitEndTimes = new int[] { 6400 }, FileName = "BMCB0331.ani" } },
        { (3, 338, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2600, 4500 }, HitEndTimes = new int[] { 4500, 6400 }, FileName = "BMCB0338.ani" } },
        { (3, 339, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4800, 5340, 5880 }, HitEndTimes = new int[] { 5340, 5880, 6420 }, FileName = "BMCB0339.ani" } },
        { (3, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5050 }, HitEndTimes = new int[] { 5200 }, FileName = "BMCB0341.ani" } },
        { (3, 342, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2580, 5330 }, HitEndTimes = new int[] { 2730, 5480 }, FileName = "BMCB0342.ani" } },
        { (3, 406, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2380 }, HitEndTimes = new int[] { 2740 }, FileName = "BMCB0406.ani" } },
        { (3, 407, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1650 }, HitEndTimes = new int[] { 2080 }, FileName = "BMCB0407.ani" } },
        { (3, 408, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1950 }, HitEndTimes = new int[] { 2440 }, FileName = "BMCB0408.ani" } },
        { (3, 409, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2840 }, HitEndTimes = new int[] { 3440 }, FileName = "BMCB0409.ani" } },
        { (3, 500, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2210, 2870, 3630 }, HitEndTimes = new int[] { 2540, 3300, 4130 }, FileName = "BMCB0500.ani" } },
        { (3, 501, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2810 }, HitEndTimes = new int[] { 3040 }, FileName = "BMCB0501.ani" } },
        { (3, 502, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2180 }, HitEndTimes = new int[] { 4530 }, FileName = "BMCB0502.ani" } },
        { (3, 503, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 600, 2560 }, HitEndTimes = new int[] { 1610, 4740 }, FileName = "BMCB0503.ani" } },
        { (3, 504, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1930, 2840 }, HitEndTimes = new int[] { 2670, 4840 }, FileName = "BMCB0504.ani" } },
        { (3, 505, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 690, 2180, 2870 }, HitEndTimes = new int[] { 2010, 2710, 4260 }, FileName = "BMCB0505.ani" } },
        { (3, 506, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2350, 3730 }, HitEndTimes = new int[] { 3240, 4690 }, FileName = "BMCB0506.ani" } },
        { (3, 507, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 460, 2610, 4430 }, HitEndTimes = new int[] { 1320, 3800, 4920 }, FileName = "BMCB0507.ani" } },
        { (3, 508, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1950 }, HitEndTimes = new int[] { 2810 }, FileName = "BMCB0508.ani" } },
        { (3, 509, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1800, 4110 }, HitEndTimes = new int[] { 2380, 5120 }, FileName = "BMCB0509.ani" } },
        { (3, 510, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2670 }, HitEndTimes = new int[] { 3850 }, FileName = "BMCB0510.ani" } },
        { (3, 511, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 430, 2640 }, HitEndTimes = new int[] { 1620, 4360 }, FileName = "BMCB0511.ani" } },
        { (3, 512, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1620 }, HitEndTimes = new int[] { 4130 }, FileName = "BMCB0512.ani" } },
        { (3, 513, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2610 }, HitEndTimes = new int[] { 3930 }, FileName = "BMCB0513.ani" } },
        { (6, 206, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3510 }, HitEndTimes = new int[] { 4060 }, FileName = "BMCD0206.ani" } },
        { (6, 207, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3360 }, HitEndTimes = new int[] { 3830 }, FileName = "BMCD0207.ani" } },
        { (6, 208, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4690 }, HitEndTimes = new int[] { 5080 }, FileName = "BMCD0208.ani" } },
        { (6, 209, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3750, 6880 }, HitEndTimes = new int[] { 5000, 8050 }, FileName = "BMCD0209.ani" } },
        { (6, 300, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2270, 3100, 6820 }, HitEndTimes = new int[] { 3560, 4390, 8330 }, FileName = "BMCD0300.ani" } },
        { (6, 305, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4320 }, HitEndTimes = new int[] { 4850 }, FileName = "BMCD0305.ani" } },
        { (6, 306, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1890, 3410, 6970 }, HitEndTimes = new int[] { 2270, 3860, 7350 }, FileName = "BMCD0306.ani" } },
        { (6, 307, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7800 }, HitEndTimes = new int[] { 8180 }, FileName = "BMCD0307.ani" } },
        { (6, 308, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2810 }, HitEndTimes = new int[] { 2970 }, FileName = "BMCD0308.ani" } },
        { (6, 309, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1720, 3200, 6480 }, HitEndTimes = new int[] { 2110, 3510, 6720 }, FileName = "BMCD0309.ani" } },
        { (6, 312, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1970, 4160, 7880 }, HitEndTimes = new int[] { 2350, 4390, 8330 }, FileName = "BMCD0312.ani" } },
        { (6, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5300 }, HitEndTimes = new int[] { 5680 }, FileName = "BMCD0313.ani" } },
        { (6, 314, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5690 }, HitEndTimes = new int[] { 6410 }, FileName = "BMCD0314.ani" } },
        { (6, 315, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2420 }, HitEndTimes = new int[] { 3330 }, FileName = "BMCD0315.ani" } },
        { (6, 316, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2638, 4366 }, HitEndTimes = new int[] { 3389, 5170 }, FileName = "BMCD0316.ani" } },
        { (6, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3260 }, HitEndTimes = new int[] { 4740 }, FileName = "BMCD0317.ani" } },
        { (6, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2911 }, HitEndTimes = new int[] { 3836 }, FileName = "BMCD0318.ani" } },
        { (6, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6020 }, HitEndTimes = new int[] { 6660 }, FileName = "BMCD0319.ani" } },
        { (6, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5140 }, HitEndTimes = new int[] { 5435 }, FileName = "BMCD0320.ani" } },
        { (6, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5180 }, HitEndTimes = new int[] { 6910 }, FileName = "BMCD0326.ani" } },
        { (6, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4564 }, HitEndTimes = new int[] { 5776 }, FileName = "BMCD0327.ani" } },
        { (6, 331, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5230 }, HitEndTimes = new int[] { 5830 }, FileName = "BMCD0331.ani" } },
        { (6, 339, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3120, 5650 }, HitEndTimes = new int[] { 4060, 7030 }, FileName = "BMCD0339.ani" } },
        { (6, 342, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4900, 5050 }, HitEndTimes = new int[] { 5050, 5200 }, FileName = "BMCD0342.ani" } },
        { (6, 406, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1410 }, HitEndTimes = new int[] { 1610 }, FileName = "BMCD0406.ani" } },
        { (6, 407, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2060 }, HitEndTimes = new int[] { 2300 }, FileName = "BMCD0407.ani" } },
        { (6, 408, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1730 }, HitEndTimes = new int[] { 1940 }, FileName = "BMCD0408.ani" } },
        { (6, 409, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1370, 3240 }, HitEndTimes = new int[] { 2810, 3870 }, FileName = "BMCD0409.ani" } },
        { (6, 500, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 980, 2890 }, HitEndTimes = new int[] { 2190, 3830 }, FileName = "BMCD0500.ani" } },
        { (6, 505, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2070 }, HitEndTimes = new int[] { 2500 }, FileName = "BMCD0505.ani" } },
        { (6, 506, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 3440 }, HitEndTimes = new int[] { 1450, 3830 }, FileName = "BMCD0506.ani" } },
        { (6, 507, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3910 }, HitEndTimes = new int[] { 4140 }, FileName = "BMCD0507.ani" } },
        { (6, 508, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1370 }, HitEndTimes = new int[] { 1570 }, FileName = "BMCD0508.ani" } },
        { (6, 509, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1210, 3350 }, HitEndTimes = new int[] { 1530, 3510 }, FileName = "BMCD0509.ani" } },
        { (6, 512, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1680, 3600 }, HitEndTimes = new int[] { 1950, 3830 }, FileName = "BMCD0512.ani" } },
        { (6, 513, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2810 }, HitEndTimes = new int[] { 3010 }, FileName = "BMCD0513.ani" } },
        { (5, 206, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5730 }, HitEndTimes = new int[] { 6760 }, FileName = "BMCE0206.ani" } },
        { (5, 207, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4800 }, HitEndTimes = new int[] { 6090 }, FileName = "BMCE0207.ani" } },
        { (5, 208, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 10260, 11450, 12080 }, HitEndTimes = new int[] { 10990, 11900, 12990 }, FileName = "BMCE0208.ani" } },
        { (5, 209, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6650 }, HitEndTimes = new int[] { 7780 }, FileName = "BMCE0209.ani" } },
        { (5, 300, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5330 }, HitEndTimes = new int[] { 6350 }, FileName = "BMCE0300.ani" } },
        { (5, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4010 }, HitEndTimes = new int[] { 5690 }, FileName = "BMCE0301.ani" } },
        { (5, 302, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5390 }, HitEndTimes = new int[] { 8080 }, FileName = "BMCE0302.ani" } },
        { (5, 303, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6710 }, HitEndTimes = new int[] { 8440 }, FileName = "BMCE0303.ani" } },
        { (5, 304, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2330, 4310, 6350 }, HitEndTimes = new int[] { 3950, 5990, 8320 }, FileName = "BMCE0304.ani" } },
        { (5, 305, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6910 }, HitEndTimes = new int[] { 9220 }, FileName = "BMCE0305.ani" } },
        { (5, 306, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 9090 }, FileName = "BMCE0306.ani" } },
        { (5, 307, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6460 }, HitEndTimes = new int[] { 9340 }, FileName = "BMCE0307.ani" } },
        { (5, 308, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2980, 4540, 7050 }, HitEndTimes = new int[] { 3800, 5090, 8270 }, FileName = "BMCE0308.ani" } },
        { (5, 309, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1970, 3730, 7390 }, HitEndTimes = new int[] { 2370, 4140, 8270 }, FileName = "BMCE0309.ani" } },
        { (5, 310, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 950, 3190, 6650 }, HitEndTimes = new int[] { 1490, 3660, 7260 }, FileName = "BMCE0310.ani" } },
        { (5, 311, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1280, 4350, 7040 }, HitEndTimes = new int[] { 3010, 6530, 9150 }, FileName = "BMCE0311.ani" } },
        { (5, 312, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3010, 6590 }, HitEndTimes = new int[] { 5500, 8960 }, FileName = "BMCE0312.ani" } },
        { (5, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5440 }, HitEndTimes = new int[] { 8640 }, FileName = "BMCE0313.ani" } },
        { (5, 314, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 7940 }, FileName = "BMCE0314.ani" } },
        { (5, 315, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3154, 4821 }, HitEndTimes = new int[] { 3616, 5170 }, FileName = "BMCE0315.ani" } },
        { (5, 316, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5434 }, HitEndTimes = new int[] { 5869 }, FileName = "BMCE0316.ani" } },
        { (5, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2714 }, HitEndTimes = new int[] { 4806 }, FileName = "BMCE0317.ani" } },
        { (5, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5044 }, HitEndTimes = new int[] { 6116 }, FileName = "BMCE0318.ani" } },
        { (5, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4753 }, HitEndTimes = new int[] { 5140 }, FileName = "BMCE0319.ani" } },
        { (5, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4708 }, HitEndTimes = new int[] { 4950 }, FileName = "BMCE0320.ani" } },
        { (5, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5966 }, HitEndTimes = new int[] { 6451 }, FileName = "BMCE0326.ani" } },
        { (5, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4514 }, HitEndTimes = new int[] { 5389 }, FileName = "BMCE0327.ani" } },
        { (5, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4120 }, HitEndTimes = new int[] { 5720 }, FileName = "BMCE0330.ani" } },
        { (5, 331, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2090 }, HitEndTimes = new int[] { 6110 }, FileName = "BMCE0331.ani" } },
        { (5, 338, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2630 }, HitEndTimes = new int[] { 7120 }, FileName = "BMCE0338.ani" } },
        { (5, 339, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2220, 5000 }, HitEndTimes = new int[] { 3800, 5850 }, FileName = "BMCE0339.ani" } },
        { (5, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5300 }, HitEndTimes = new int[] { 5450 }, FileName = "BMCE0341.ani" } },
        { (5, 342, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5150, 5300, 5450 }, HitEndTimes = new int[] { 5300, 5450, 5600 }, FileName = "BMCE0342.ani" } },
        { (5, 401, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4180 }, HitEndTimes = new int[] { 5400 }, FileName = "BMCE0401.ani" } },
        { (1, 206, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3450 }, HitEndTimes = new int[] { 4000 }, FileName = "BMCK0206.ani" } },
        { (1, 207, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6950 }, HitEndTimes = new int[] { 7180 }, FileName = "BMCK0207.ani" } },
        { (1, 208, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3490, 4290, 5150 }, HitEndTimes = new int[] { 3860, 4460, 5370 }, FileName = "BMCK0208.ani" } },
        { (1, 209, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4910 }, HitEndTimes = new int[] { 5270 }, FileName = "BMCK0209.ani" } },
        { (1, 300, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7660 }, HitEndTimes = new int[] { 8720 }, FileName = "BMCK0300.ani" } },
        { (1, 301, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2370, 6140 }, HitEndTimes = new int[] { 2880, 6660 }, FileName = "BMCK0301.ani" } },
        { (1, 302, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2430, 7300 }, HitEndTimes = new int[] { 4930, 9340 }, FileName = "BMCK0302.ani" } },
        { (1, 303, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1020, 4160, 7300 }, HitEndTimes = new int[] { 2880, 6020, 9090 }, FileName = "BMCK0303.ani" } },
        { (1, 304, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7040 }, HitEndTimes = new int[] { 9470 }, FileName = "BMCK0304.ani" } },
        { (1, 305, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1790, 4100, 8380 }, HitEndTimes = new int[] { 3260, 5570, 9280 }, FileName = "BMCK0305.ani" } },
        { (1, 306, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3840, 5180, 6720 }, HitEndTimes = new int[] { 4610, 5950, 8060 }, FileName = "BMCK0306.ani" } },
        { (1, 307, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8130 }, HitEndTimes = new int[] { 9220 }, FileName = "BMCK0307.ani" } },
        { (1, 308, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 390, 2100, 6040 }, HitEndTimes = new int[] { 850, 2890, 6700 }, FileName = "BMCK0308.ani" } },
        { (1, 309, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4120 }, HitEndTimes = new int[] { 4690 }, FileName = "BMCK0309.ani" } },
        { (1, 310, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4140 }, HitEndTimes = new int[] { 5780 }, FileName = "BMCK0310.ani" } },
        { (1, 311, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1790, 3350, 6240 }, HitEndTimes = new int[] { 2770, 4860, 7920 }, FileName = "BMCK0311.ani" } },
        { (1, 312, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3460, 7230 }, HitEndTimes = new int[] { 5890, 8960 }, FileName = "BMCK0312.ani" } },
        { (1, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5500 }, HitEndTimes = new int[] { 9070 }, FileName = "BMCK0313.ani" } },
        { (1, 314, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6720 }, HitEndTimes = new int[] { 7040 }, FileName = "BMCK0314.ani" } },
        { (1, 315, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3442 }, HitEndTimes = new int[] { 3616 }, FileName = "BMCK0315.ani" } },
        { (1, 316, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2585, 5610 }, HitEndTimes = new int[] { 2873, 5640 }, FileName = "BMCK0316.ani" } },
        { (1, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3684 }, HitEndTimes = new int[] { 5193 }, FileName = "BMCK0317.ani" } },
        { (1, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3540 }, HitEndTimes = new int[] { 4222 }, FileName = "BMCK0318.ani" } },
        { (1, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4806 }, HitEndTimes = new int[] { 5238 }, FileName = "BMCK0319.ani" } },
        { (1, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4321 }, HitEndTimes = new int[] { 4564 }, FileName = "BMCK0320.ani" } },
        { (1, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3836 }, HitEndTimes = new int[] { 4222 }, FileName = "BMCK0326.ani" } },
        { (1, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5243 }, HitEndTimes = new int[] { 6110 }, FileName = "BMCK0327.ani" } },
        { (1, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4900 }, HitEndTimes = new int[] { 4990 }, FileName = "BMCK0330.ani" } },
        { (1, 331, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1540, 2740, 4900 }, HitEndTimes = new int[] { 2180, 3090, 6900 }, FileName = "BMCK0331.ani" } },
        { (1, 338, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4490 }, HitEndTimes = new int[] { 6000 }, FileName = "BMCK0338.ani" } },
        { (1, 339, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5000, 5500, 6000 }, HitEndTimes = new int[] { 5500, 6000, 6500 }, FileName = "BMCK0339.ani" } },
        { (1, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5380 }, HitEndTimes = new int[] { 5530 }, FileName = "BMCK0341.ani" } },
        { (1, 342, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4900, 6700 }, HitEndTimes = new int[] { 5050, 6850 }, FileName = "BMCK0342.ani" } },
        { (1, 406, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2840 }, HitEndTimes = new int[] { 3270 }, FileName = "BMCK0406.ani" } },
        { (1, 407, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3440 }, HitEndTimes = new int[] { 3770 }, FileName = "BMCK0407.ani" } },
        { (1, 408, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1120, 1950, 2770 }, HitEndTimes = new int[] { 1490, 2410, 3170 }, FileName = "BMCK0408.ani" } },
        { (1, 409, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1350 }, HitEndTimes = new int[] { 2050 }, FileName = "BMCK0409.ani" } },
        { (1, 500, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2810 }, HitEndTimes = new int[] { 3070 }, FileName = "BMCK0500.ani" } },
        { (1, 501, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1590 }, HitEndTimes = new int[] { 1850 }, FileName = "BMCK0501.ani" } },
        { (1, 502, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2540 }, HitEndTimes = new int[] { 4290 }, FileName = "BMCK0502.ani" } },
        { (1, 503, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3630 }, HitEndTimes = new int[] { 4620 }, FileName = "BMCK0503.ani" } },
        { (1, 504, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3500 }, HitEndTimes = new int[] { 4720 }, FileName = "BMCK0504.ani" } },
        { (1, 505, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1450, 3630 }, HitEndTimes = new int[] { 2180, 4620 }, FileName = "BMCK0505.ani" } },
        { (1, 506, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1680, 2310, 3240 }, HitEndTimes = new int[] { 2150, 2910, 4230 }, FileName = "BMCK0506.ani" } },
        { (1, 507, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3860 }, HitEndTimes = new int[] { 4890 }, FileName = "BMCK0507.ani" } },
        { (1, 508, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 170, 1050, 2710 }, HitEndTimes = new int[] { 880, 1490, 3260 }, FileName = "BMCK0508.ani" } },
        { (1, 509, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1860 }, HitEndTimes = new int[] { 2640 }, FileName = "BMCK0509.ani" } },
        { (1, 510, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 750 }, HitEndTimes = new int[] { 1860 }, FileName = "BMCK0510.ani" } },
        { (1, 511, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 960, 2280, 4000 }, HitEndTimes = new int[] { 1550, 3340, 4760 }, FileName = "BMCK0511.ani" } },
        { (1, 512, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1590, 3170 }, HitEndTimes = new int[] { 2940, 4720 }, FileName = "BMCK0512.ani" } },
        { (1, 513, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2110 }, HitEndTimes = new int[] { 4330 }, FileName = "BMCK0513.ani" } },
        { (7, 206, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "BMCM0206.ani" } },
        { (7, 207, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "BMCM0207.ani" } },
        { (7, 208, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "BMCM0208.ani" } },
        { (7, 209, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "BMCM0209.ani" } },
        { (7, 300, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "BMCM0300.ani" } },
        { (7, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "BMCM0301.ani" } },
        { (7, 302, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "BMCM0302.ani" } },
        { (7, 303, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "BMCM0303.ani" } },
        { (7, 304, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "BMCM0304.ani" } },
        { (7, 305, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "BMCM0305.ani" } },
        { (7, 306, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "BMCM0306.ani" } },
        { (7, 307, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "BMCM0307.ani" } },
        { (7, 308, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6660 }, HitEndTimes = new int[] { 6850 }, FileName = "BMCM0308.ani" } },
        { (7, 309, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6660 }, HitEndTimes = new int[] { 6850 }, FileName = "BMCM0309.ani" } },
        { (7, 310, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6660 }, HitEndTimes = new int[] { 6850 }, FileName = "BMCM0310.ani" } },
        { (7, 311, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5380 }, HitEndTimes = new int[] { 5760 }, FileName = "BMCM0311.ani" } },
        { (7, 312, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5380 }, HitEndTimes = new int[] { 5760 }, FileName = "BMCM0312.ani" } },
        { (7, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4100 }, HitEndTimes = new int[] { 4610 }, FileName = "BMCM0313.ani" } },
        { (7, 314, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4100 }, HitEndTimes = new int[] { 4610 }, FileName = "BMCM0314.ani" } },
        { (7, 315, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3737 }, HitEndTimes = new int[] { 4564 }, FileName = "BMCM0315.ani" } },
        { (7, 316, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3737 }, HitEndTimes = new int[] { 4564 }, FileName = "BMCM0316.ani" } },
        { (7, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5625 }, FileName = "BMCM0317.ani" } },
        { (7, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5625 }, FileName = "BMCM0318.ani" } },
        { (7, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5094 }, HitEndTimes = new int[] { 5723 }, FileName = "BMCM0319.ani" } },
        { (7, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5094 }, HitEndTimes = new int[] { 5723 }, FileName = "BMCM0320.ani" } },
        { (7, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4122 }, HitEndTimes = new int[] { 5333 }, FileName = "BMCM0326.ani" } },
        { (7, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4852 }, HitEndTimes = new int[] { 5625 }, FileName = "BMCM0327.ani" } },
        { (7, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5370 }, HitEndTimes = new int[] { 6580 }, FileName = "BMCM0330.ani" } },
        { (7, 331, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5550 }, HitEndTimes = new int[] { 6420 }, FileName = "BMCM0331.ani" } },
        { (7, 338, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5300 }, HitEndTimes = new int[] { 6270 }, FileName = "BMCM0338.ani" } },
        { (7, 339, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1000, 3600 }, HitEndTimes = new int[] { 3600, 6200 }, FileName = "BMCM0339.ani" } },
        { (7, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5300 }, HitEndTimes = new int[] { 5450 }, FileName = "BMCM0341.ani" } },
        { (7, 342, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1900, 3100, 5450 }, HitEndTimes = new int[] { 2050, 3250, 5600 }, FileName = "BMCM0342.ani" } },
        { (7, 401, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4180 }, HitEndTimes = new int[] { 5400 }, FileName = "BMCM0401.ani" } },
        { (2, 206, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5450, 6190, 7560 }, HitEndTimes = new int[] { 5950, 6750, 8240 }, FileName = "BMCS0206.ani" } },
        { (2, 207, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5810 }, HitEndTimes = new int[] { 6230 }, FileName = "BMCS0207.ani" } },
        { (2, 208, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1600, 4100, 7040 }, HitEndTimes = new int[] { 1980, 4540, 7490 }, FileName = "BMCS0208.ani" } },
        { (2, 209, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1260, 7240 }, HitEndTimes = new int[] { 1800, 7720 }, FileName = "BMCS0209.ani" } },
        { (2, 300, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6110 }, HitEndTimes = new int[] { 6650 }, FileName = "BMCS0300.ani" } },
        { (2, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5180 }, HitEndTimes = new int[] { 5890 }, FileName = "BMCS0301.ani" } },
        { (2, 302, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2750, 5250, 7040 }, HitEndTimes = new int[] { 4420, 6660, 9410 }, FileName = "BMCS0302.ani" } },
        { (2, 303, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7870 }, HitEndTimes = new int[] { 9410 }, FileName = "BMCS0303.ani" } },
        { (2, 304, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7680 }, HitEndTimes = new int[] { 9280 }, FileName = "BMCS0304.ani" } },
        { (2, 305, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1740, 6350 }, HitEndTimes = new int[] { 4250, 8380 }, FileName = "BMCS0305.ani" } },
        { (2, 306, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2510, 6170 }, HitEndTimes = new int[] { 3410, 8380 }, FileName = "BMCS0306.ani" } },
        { (2, 307, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1660, 4420, 6660 }, HitEndTimes = new int[] { 2820, 5890, 9020 }, FileName = "BMCS0307.ani" } },
        { (2, 308, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1700, 4880, 6850 }, HitEndTimes = new int[] { 2100, 5900, 7600 }, FileName = "BMCS0308.ani" } },
        { (2, 309, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1130, 2490, 4210 }, HitEndTimes = new int[] { 1780, 3740, 4810 }, FileName = "BMCS0309.ani" } },
        { (2, 310, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2580, 6240 }, HitEndTimes = new int[] { 3320, 6980 }, FileName = "BMCS0310.ani" } },
        { (2, 311, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1660, 4540, 6660 }, HitEndTimes = new int[] { 3710, 5760, 8640 }, FileName = "BMCS0311.ani" } },
        { (2, 312, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2500, 6910 }, HitEndTimes = new int[] { 5890, 8960 }, FileName = "BMCS0312.ani" } },
        { (2, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7170 }, HitEndTimes = new int[] { 9220 }, FileName = "BMCS0313.ani" } },
        { (2, 314, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3650, 4800, 6530 }, HitEndTimes = new int[] { 3900, 4990, 7040 }, FileName = "BMCS0314.ani" } },
        { (2, 315, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3730 }, HitEndTimes = new int[] { 4935 }, FileName = "BMCS0315.ani" } },
        { (2, 316, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1781, 5511 }, HitEndTimes = new int[] { 2070, 5913 }, FileName = "BMCS0316.ani" } },
        { (2, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3442 }, HitEndTimes = new int[] { 4996 }, FileName = "BMCS0317.ani" } },
        { (2, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3593 }, HitEndTimes = new int[] { 4655 }, FileName = "BMCS0318.ani" } },
        { (2, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4996 }, HitEndTimes = new int[] { 5579 }, FileName = "BMCS0319.ani" } },
        { (2, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4465 }, HitEndTimes = new int[] { 4806 }, FileName = "BMCS0320.ani" } },
        { (2, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3976 }, HitEndTimes = new int[] { 5292 }, FileName = "BMCS0326.ani" } },
        { (2, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3688 }, HitEndTimes = new int[] { 5143 }, FileName = "BMCS0327.ani" } },
        { (2, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3010 }, HitEndTimes = new int[] { 5090 }, FileName = "BMCS0330.ani" } },
        { (2, 331, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2630, 3760, 5160 }, HitEndTimes = new int[] { 3060, 4090, 5790 }, FileName = "BMCS0331.ani" } },
        { (2, 338, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5080 }, HitEndTimes = new int[] { 6840 }, FileName = "BMCS0338.ani" } },
        { (2, 339, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2930, 4720 }, HitEndTimes = new int[] { 3550, 6730 }, FileName = "BMCS0339.ani" } },
        { (2, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5810 }, HitEndTimes = new int[] { 5960 }, FileName = "BMCS0341.ani" } },
        { (2, 342, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 6050, 6200 }, HitEndTimes = new int[] { 6200, 6350 }, FileName = "BMCS0342.ani" } },
        { (2, 406, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1550, 2810, 3500 }, HitEndTimes = new int[] { 1980, 3140, 3770 }, FileName = "BMCS0406.ani" } },
        { (2, 407, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2150 }, HitEndTimes = new int[] { 2410 }, FileName = "BMCS0407.ani" } },
        { (2, 408, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2680 }, HitEndTimes = new int[] { 3200 }, FileName = "BMCS0408.ani" } },
        { (2, 409, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3860 }, HitEndTimes = new int[] { 4460 }, FileName = "BMCS0409.ani" } },
        { (2, 500, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2770 }, HitEndTimes = new int[] { 2870 }, FileName = "BMCS0500.ani" } },
        { (2, 501, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1420, 2080 }, HitEndTimes = new int[] { 1780, 2680 }, FileName = "BMCS0501.ani" } },
        { (2, 502, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 710, 3310, 4790 }, HitEndTimes = new int[] { 1560, 4120, 5460 }, FileName = "BMCS0502.ani" } },
        { (2, 503, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3070 }, HitEndTimes = new int[] { 4590 }, FileName = "BMCS0503.ani" } },
        { (2, 504, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3830 }, HitEndTimes = new int[] { 4500 }, FileName = "BMCS0504.ani" } },
        { (2, 505, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2840 }, HitEndTimes = new int[] { 4200 }, FileName = "BMCS0505.ani" } },
        { (2, 506, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 2970 }, HitEndTimes = new int[] { 1980, 4590 }, FileName = "BMCS0506.ani" } },
        { (2, 507, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1260, 2870 }, HitEndTimes = new int[] { 2350, 4460 }, FileName = "BMCS0507.ani" } },
        { (2, 508, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 140, 1760 }, HitEndTimes = new int[] { 410, 2410 }, FileName = "BMCS0508.ani" } },
        { (2, 509, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 540, 1760, 3220 }, HitEndTimes = new int[] { 850, 2030, 3560 }, FileName = "BMCS0509.ani" } },
        { (2, 510, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 30, 2340 }, HitEndTimes = new int[] { 440, 3050 }, FileName = "BMCS0510.ani" } },
        { (2, 511, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1650, 3270 }, HitEndTimes = new int[] { 2540, 4530 }, FileName = "BMCS0511.ani" } },
        { (2, 512, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 730, 3140 }, HitEndTimes = new int[] { 1980, 4620 }, FileName = "BMCS0512.ani" } },
        { (2, 513, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3010 }, HitEndTimes = new int[] { 4460 }, FileName = "BMCS0513.ani" } },
        { (8, 307, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7350 }, HitEndTimes = new int[] { 7740 }, FileName = "BMCV0307.ani" } },
        { (8, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4512 }, HitEndTimes = new int[] { 4850 }, FileName = "BMCV0326.ani" } },
        { (8, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7200 }, HitEndTimes = new int[] { 7740 }, FileName = "BMCV0326_1.ani" } },
        { (8, 327, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4045, 4465, 4898, 5290, 5724 }, HitEndTimes = new int[] { 4272, 4658, 5092, 5483, 5879 }, FileName = "BMCV0327.ani" } },
        { (8, 327, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 7746, 8550, 9380, 10130, 10960 }, HitEndTimes = new int[] { 8180, 8920, 9750, 10500, 11240 }, FileName = "BMCV0327_1.ani" } },
        { (8, 328, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4850 }, HitEndTimes = new int[] { 5095 }, FileName = "BMCV0328.ani" } },
        { (8, 328, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7740 }, HitEndTimes = new int[] { 8130 }, FileName = "BMCV0328_1.ani" } },
        { (10, 203, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1790, 3820 }, HitEndTimes = new int[] { 1890, 3920 }, FileName = "BMCZ0203.ani" } },
        { (10, 204, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2020 }, HitEndTimes = new int[] { 2120 }, FileName = "BMCZ0204.ani" } },
        { (10, 205, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1930 }, HitEndTimes = new int[] { 2030 }, FileName = "BMCZ0205.ani" } },
        { (10, 206, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2220, 2690, 3950, 5210 }, HitEndTimes = new int[] { 2270, 2740, 4000, 5260 }, FileName = "BMCZ0206.ani" } },
        { (10, 207, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2220, 2690, 3950, 5210, 6300 }, HitEndTimes = new int[] { 2270, 2740, 4000, 5260, 6350 }, FileName = "BMCZ0207.ani" } },
        { (10, 208, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2590, 3000, 5280, 6520, 8440 }, HitEndTimes = new int[] { 2690, 3100, 5300, 6620, 8540 }, FileName = "BMCZ0208.ani" } },
        { (10, 209, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3760 }, HitEndTimes = new int[] { 3860 }, FileName = "BMCZ0209.ani" } },
        { (10, 210, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4210 }, HitEndTimes = new int[] { 5500 }, FileName = "BMCZ0210.ani" } },
        { (10, 211, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 910, 2670, 4280, 7160 }, HitEndTimes = new int[] { 1010, 2770, 4380, 7260 }, FileName = "BMCZ0211.ani" } },
        { (10, 213, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3670 }, HitEndTimes = new int[] { 3770 }, FileName = "BMCZ0213.ani" } },
        { (10, 214, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 950, 2650, 4250, 7230 }, HitEndTimes = new int[] { 1050, 2750, 4350, 7330 }, FileName = "BMCZ0214.ani" } },
        { (10, 215, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 810, 2090, 4240 }, HitEndTimes = new int[] { 860, 2140, 4340 }, FileName = "BMCZ0215.ani" } },
        { (10, 217, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1860, 3900 }, HitEndTimes = new int[] { 1960, 4000 }, FileName = "BMCZ0217.ani" } },
        { (10, 218, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1340, 3860 }, HitEndTimes = new int[] { 1440, 3960 }, FileName = "BMCZ0218.ani" } },
        { (10, 219, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1240, 2560 }, HitEndTimes = new int[] { 1340, 2660 }, FileName = "BMCZ0219.ani" } },
        { (10, 220, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1700, 3830 }, HitEndTimes = new int[] { 1800, 3930 }, FileName = "BMCZ0220.ani" } },
        { (10, 221, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1670, 4350 }, HitEndTimes = new int[] { 1770, 4450 }, FileName = "BMCZ0221.ani" } },
        { (10, 222, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2430, 6270 }, HitEndTimes = new int[] { 2530, 6370 }, FileName = "BMCZ0222.ani" } },
        { (10, 223, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 3060 }, HitEndTimes = new int[] { 1760, 3160 }, FileName = "BMCZ0223.ani" } },
        { (10, 300, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1750, 4200 }, HitEndTimes = new int[] { 1850, 4300 }, FileName = "BMCZ0300.ani" } },
        { (10, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4000 }, HitEndTimes = new int[] { 4100 }, FileName = "BMCZ0301.ani" } },
        { (10, 302, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 710, 1570, 2280, 2920, 6130 }, HitEndTimes = new int[] { 810, 1670, 2380, 3020, 6230 }, FileName = "BMCZ0302.ani" } },
        { (10, 303, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 1150, 2430, 3970, 6500 }, HitEndTimes = new int[] { 1250, 2530, 4070, 6600 }, FileName = "BMCZ0303.ani" } },
        { (10, 304, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1770, 3450 }, HitEndTimes = new int[] { 1870, 3550 }, FileName = "BMCZ0304.ani" } },
        { (10, 305, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2430, 4230, 5420 }, HitEndTimes = new int[] { 2530, 4330, 5520 }, FileName = "BMCZ0305.ani" } },
        { (10, 306, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1700, 2850, 4450 }, HitEndTimes = new int[] { 1800, 2950, 4550 }, FileName = "BMCZ0306.ani" } },
        { (10, 307, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 500, 1900, 5570 }, HitEndTimes = new int[] { 600, 2000, 5670 }, FileName = "BMCZ0307.ani" } },
        { (10, 308, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2630, 4130, 6170, 7720 }, HitEndTimes = new int[] { 2730, 4230, 6270, 7820 }, FileName = "BMCZ0308.ani" } },
        { (10, 309, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2630, 4130, 6170, 7720, 10840 }, HitEndTimes = new int[] { 2730, 4230, 6270, 7820, 11840 }, FileName = "BMCZ0309.ani" } },
        { (10, 310, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1300, 4400 }, HitEndTimes = new int[] { 1400, 4500 }, FileName = "BMCZ0310.ani" } },
        { (10, 311, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2570, 3230, 3890, 4490 }, HitEndTimes = new int[] { 2620, 3280, 3940, 4540 }, FileName = "BMCZ0311.ani" } },
        { (10, 312, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2080, 2430, 2890, 3240, 3930 }, HitEndTimes = new int[] { 2180, 2530, 2990, 3340, 4030 }, FileName = "BMCZ0312.ani" } },
        { (10, 313, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1250, 4150 }, HitEndTimes = new int[] { 1350, 4250 }, FileName = "BMCZ0313.ani" } },
        { (10, 314, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3700 }, HitEndTimes = new int[] { 3800 }, FileName = "BMCZ0314.ani" } },
        { (10, 315, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 1211, 2186, 3397, 4999 }, HitEndTimes = new int[] { 1282, 2257, 3468, 5070 }, FileName = "BMCZ0315.ani" } },
        { (10, 316, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4759 }, HitEndTimes = new int[] { 4843 }, FileName = "BMCZ0316.ani" } },
        { (10, 317, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 844, 1687, 2428, 3055, 4320 }, HitEndTimes = new int[] { 904, 1747, 2489, 3115, 4381 }, FileName = "BMCZ0317.ani" } },
        { (10, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3878 }, HitEndTimes = new int[] { 3961 }, FileName = "BMCZ0318.ani" } },
        { (10, 319, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 586, 1564, 2466, 3930 }, HitEndTimes = new int[] { 663, 1641, 2543, 4007 }, FileName = "BMCZ0319.ani" } },
        { (10, 320, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 924, 1945, 2553, 3104, 5147 }, HitEndTimes = new int[] { 1005, 2026, 2634, 3185, 5228 }, FileName = "BMCZ0320.ani" } },
        { (10, 326, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1330, 4080 }, HitEndTimes = new int[] { 1430, 4180 }, FileName = "BMCZ0326.ani" } },
        { (10, 327, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2444, 4183 }, HitEndTimes = new int[] { 2538, 4277 }, FileName = "BMCZ0327.ani" } },
        { (10, 330, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2830, 5550 }, HitEndTimes = new int[] { 3060, 6000 }, FileName = "BMCZ0330.ani" } },
        { (10, 331, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3350, 4550 }, HitEndTimes = new int[] { 3540, 5320 }, FileName = "BMCZ0331.ani" } },
        { (10, 338, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3880, 5450, 6530 }, HitEndTimes = new int[] { 4500, 5740, 6870 }, FileName = "BMCZ0338.ani" } },
        { (10, 339, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2280, 3350, 5860 }, HitEndTimes = new int[] { 3010, 4460, 6580 }, FileName = "BMCZ0339.ani" } },
        { (10, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4750 }, HitEndTimes = new int[] { 4900 }, FileName = "BMCZ0341.ani" } },
        { (10, 342, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4650, 4800, 4950 }, HitEndTimes = new int[] { 4800, 4950, 5100 }, FileName = "BMCZ0342.ani" } },
        { (4, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 2440 }, HitEndTimes = new int[] { 140, 2550 }, FileName = "MCA0103.ani" } },
        { (4, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 160, 2090 }, HitEndTimes = new int[] { 170, 2110 }, FileName = "MCA0104.ani" } },
        { (4, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 110, 2040 }, HitEndTimes = new int[] { 120, 2110 }, FileName = "MCA0105.ani" } },
        { (4, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2350 }, HitEndTimes = new int[] { 2700 }, FileName = "MCA0112.ani" } },
        { (4, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1200 }, HitEndTimes = new int[] { 2200 }, FileName = "MCA0113.ani" } },
        { (4, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2070 }, HitEndTimes = new int[] { 81, 2071 }, FileName = "MCA0121.ani" } },
        { (4, 123, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 650 }, HitEndTimes = new int[] { 12220 }, FileName = "MCA0123.ani" } },
        { (4, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCA0124.ani" } },
        { (4, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2070 }, HitEndTimes = new int[] { 81, 2071 }, FileName = "MCA0125.ani" } },
        { (4, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1530 }, HitEndTimes = new int[] { 5340 }, FileName = "MCA0126.ani" } },
        { (4, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1530 }, HitEndTimes = new int[] { 5250 }, FileName = "MCA0127.ani" } },
        { (4, 128, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCA0128.ani" } },
        { (4, 129, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCA0129.ani" } },
        { (4, 130, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCA0130.ani" } },
        { (4, 131, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCA0131.ani" } },
        { (4, 132, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCA0132.ani" } },
        { (4, 133, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCA0133.ani" } },
        { (4, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2740 }, HitEndTimes = new int[] { 7210 }, FileName = "MCA0134.ani" } },
        { (4, 136, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1610 }, HitEndTimes = new int[] { 2940 }, FileName = "MCA0136.ani" } },
        { (4, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCA0142.ani" } },
        { (4, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2040 }, HitEndTimes = new int[] { 160, 2250 }, FileName = "MCA0143.ani" } },
        { (4, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCA0145.ani" } },
        { (4, 163, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7810 }, HitEndTimes = new int[] { 11080 }, FileName = "MCA0163.ani" } },
        { (4, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCA0166.ani" } },
        { (4, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3880 }, HitEndTimes = new int[] { 6040 }, FileName = "MCA0202.ani" } },
        { (4, 203, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1450 }, HitEndTimes = new int[] { 4660 }, FileName = "MCA0203.ani" } },
        { (4, 204, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4400 }, HitEndTimes = new int[] { 6640 }, FileName = "MCA0204.ani" } },
        { (4, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1540 }, HitEndTimes = new int[] { 2460 }, FileName = "MCA0205.ani" } },
        { (4, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 830, 7550 }, HitEndTimes = new int[] { 1220, 8190 }, FileName = "MCA0210.ani" } },
        { (4, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 380, 8450 }, HitEndTimes = new int[] { 680, 9130 }, FileName = "MCA0211.ani" } },
        { (4, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 7810 }, HitEndTimes = new int[] { 380, 8700 }, FileName = "MCA0212.ani" } },
        { (4, 251, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1770 }, HitEndTimes = new int[] { 5850 }, FileName = "MCA0251.ani" } },
        { (4, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7040 }, HitEndTimes = new int[] { 8190 }, FileName = "MCA0314.ani" } },
        { (4, 317, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1530 }, HitEndTimes = new int[] { 5340 }, FileName = "MCA0317.ani" } },
        { (4, 402, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1220 }, HitEndTimes = new int[] { 2750 }, FileName = "MCA0402.ani" } },
        { (4, 403, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 270 }, HitEndTimes = new int[] { 2070 }, FileName = "MCA0403.ani" } },
        { (4, 404, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1830 }, HitEndTimes = new int[] { 3090 }, FileName = "MCA0404.ani" } },
        { (4, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCA0900.ani" } },
        { (3, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2350 }, HitEndTimes = new int[] { 2530 }, FileName = "MCB0112.ani" } },
        { (3, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1140 }, HitEndTimes = new int[] { 1480 }, FileName = "MCB0113.ani" } },
        { (3, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 2090 }, HitEndTimes = new int[] { 240, 2250 }, FileName = "MCB0121.ani" } },
        { (3, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCB0124.ani" } },
        { (3, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 2090 }, HitEndTimes = new int[] { 300, 2310 }, FileName = "MCB0125.ani" } },
        { (3, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3840 }, HitEndTimes = new int[] { 4000 }, FileName = "MCB0126.ani" } },
        { (3, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3730 }, HitEndTimes = new int[] { 3900 }, FileName = "MCB0127.ani" } },
        { (3, 128, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2970, 4980, 8400 }, HitEndTimes = new int[] { 3640, 5350, 9510 }, FileName = "MCB0128.ani" } },
        { (3, 129, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3370, 6300, 10830 }, HitEndTimes = new int[] { 3820, 6920, 11720 }, FileName = "MCB0129.ani" } },
        { (3, 130, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2220, 5240, 10740 }, HitEndTimes = new int[] { 3020, 5590, 11100 }, FileName = "MCB0130.ani" } },
        { (3, 131, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3420, 7600, 12060 }, HitEndTimes = new int[] { 3990, 8170, 12920 }, FileName = "MCB0131.ani" } },
        { (3, 132, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2580, 5350, 11890 }, HitEndTimes = new int[] { 3570, 7230, 12490 }, FileName = "MCB0132.ani" } },
        { (3, 133, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 920, 4010, 6040 }, HitEndTimes = new int[] { 1120, 4470, 6310 }, FileName = "MCB0133.ani" } },
        { (3, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3140 }, HitEndTimes = new int[] { 4000 }, FileName = "MCB0134.ani" } },
        { (3, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCB0142.ani" } },
        { (3, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 30, 2040 }, HitEndTimes = new int[] { 130, 2170 }, FileName = "MCB0143.ani" } },
        { (3, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCB0145.ani" } },
        { (3, 163, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 10450 }, HitEndTimes = new int[] { 12220 }, FileName = "MCB0163.ani" } },
        { (3, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCB0166.ani" } },
        { (3, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8550 }, HitEndTimes = new int[] { 10030 }, FileName = "MCB0201.ani" } },
        { (3, 202, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3120, 8770 }, HitEndTimes = new int[] { 4460, 9590 }, FileName = "MCB0202.ani" } },
        { (3, 203, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 6240, 7510, 9440 }, HitEndTimes = new int[] { 6760, 8030, 10260 }, FileName = "MCB0203.ani" } },
        { (3, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2080 }, HitEndTimes = new int[] { 2610 }, FileName = "MCB0205.ani" } },
        { (3, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1150, 7550 }, HitEndTimes = new int[] { 1280, 8130 }, FileName = "MCB0210.ani" } },
        { (3, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 7680 }, HitEndTimes = new int[] { 1730, 8060 }, FileName = "MCB0211.ani" } },
        { (3, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 7420 }, HitEndTimes = new int[] { 1340, 7870 }, FileName = "MCB0212.ani" } },
        { (3, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 6980 }, FileName = "MCB0314.ani" } },
        { (3, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2820 }, HitEndTimes = new int[] { 3270 }, FileName = "MCB0401.ani" } },
        { (3, 402, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2810 }, HitEndTimes = new int[] { 3200 }, FileName = "MCB0402.ani" } },
        { (3, 403, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 890, 2050, 3700 }, HitEndTimes = new int[] { 1260, 2410, 4290 }, FileName = "MCB0403.ani" } },
        { (3, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCB0900.ani" } },
        { (6, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 200, 2640 }, HitEndTimes = new int[] { 730, 2940 }, FileName = "MCD0103.ani" } },
        { (6, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 160, 2090 }, HitEndTimes = new int[] { 400, 2330 }, FileName = "MCD0104.ani" } },
        { (6, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 270, 2150 }, HitEndTimes = new int[] { 430, 2330 }, FileName = "MCD0105.ani" } },
        { (6, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2380 }, HitEndTimes = new int[] { 3210 }, FileName = "MCD0112.ani" } },
        { (6, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1160 }, HitEndTimes = new int[] { 1770 }, FileName = "MCD0113.ani" } },
        { (6, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1050, 2950 }, HitEndTimes = new int[] { 1290, 3110 }, FileName = "MCD0121.ani" } },
        { (6, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCD0124.ani" } },
        { (6, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1130, 2870 }, HitEndTimes = new int[] { 1260, 3060 }, FileName = "MCD0125.ani" } },
        { (6, 126, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1900, 3970 }, HitEndTimes = new int[] { 2430, 4600 }, FileName = "MCD0126.ani" } },
        { (6, 127, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1720, 3490 }, HitEndTimes = new int[] { 2200, 4400 }, FileName = "MCD0127.ani" } },
        { (6, 129, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2080, 2770, 6240, 7800, 11790 }, HitEndTimes = new int[] { 2430, 3730, 7110, 8240, 12830 }, FileName = "MCD0129.ani" } },
        { (6, 130, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2250, 4600, 5720, 6850, 10150 }, HitEndTimes = new int[] { 2950, 5290, 6330, 8410, 11190 }, FileName = "MCD0130.ani" } },
        { (6, 131, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1470, 2950, 5380, 7630, 10230 }, HitEndTimes = new int[] { 2080, 3470, 6240, 8410, 12570 }, FileName = "MCD0131.ani" } },
        { (6, 132, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1130, 3210, 6240, 7540, 10490 }, HitEndTimes = new int[] { 2340, 4080, 7110, 8410, 11620 }, FileName = "MCD0132.ani" } },
        { (6, 133, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1990, 3380, 6330, 7460, 10230 }, HitEndTimes = new int[] { 2770, 4680, 7280, 8240, 11190 }, FileName = "MCD0133.ani" } },
        { (6, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4300 }, HitEndTimes = new int[] { 5080 }, FileName = "MCD0134.ani" } },
        { (6, 136, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2970 }, HitEndTimes = new int[] { 3450 }, FileName = "MCD0136.ani" } },
        { (6, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCD0142.ani" } },
        { (6, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 940, 2870 }, HitEndTimes = new int[] { 1070, 3060 }, FileName = "MCD0143.ani" } },
        { (6, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCD0145.ani" } },
        { (6, 152, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1900, 3970 }, HitEndTimes = new int[] { 2650, 4550 }, FileName = "MCD0152.ani" } },
        { (6, 153, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1720, 3490 }, HitEndTimes = new int[] { 2200, 4020 }, FileName = "MCD0153.ani" } },
        { (6, 154, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2400 }, HitEndTimes = new int[] { 3260 }, FileName = "MCD0154.ani" } },
        { (6, 155, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1190 }, HitEndTimes = new int[] { 2000 }, FileName = "MCD0155.ani" } },
        { (6, 156, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1470, 3210, 5980, 10670, 11880 }, HitEndTimes = new int[] { 2170, 4080, 6760, 11270, 12660 }, FileName = "MCD0156.ani" } },
        { (6, 157, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2970 }, HitEndTimes = new int[] { 3450 }, FileName = "MCD0157.ani" } },
        { (6, 158, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3620 }, HitEndTimes = new int[] { 3840 }, FileName = "MCD0158.ani" } },
        { (6, 159, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 740 }, HitEndTimes = new int[] { 1360 }, FileName = "MCD0159.ani" } },
        { (6, 162, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 2700 }, HitEndTimes = new int[] { 1410, 3200 }, FileName = "MCD0162.ani" } },
        { (6, 163, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 10680 }, HitEndTimes = new int[] { 13780 }, FileName = "MCD0163.ani" } },
        { (6, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCD0166.ani" } },
        { (6, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5230 }, HitEndTimes = new int[] { 7350 }, FileName = "MCD0201.ani" } },
        { (6, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6090 }, HitEndTimes = new int[] { 7270 }, FileName = "MCD0202.ani" } },
        { (6, 203, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3850 }, HitEndTimes = new int[] { 5230 }, FileName = "MCD0203.ani" } },
        { (6, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8380 }, HitEndTimes = new int[] { 900, 9020 }, FileName = "MCD0210.ani" } },
        { (6, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8900 }, HitEndTimes = new int[] { 1020, 9280 }, FileName = "MCD0211.ani" } },
        { (6, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8640 }, HitEndTimes = new int[] { 1020, 9090 }, FileName = "MCD0212.ani" } },
        { (6, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5690 }, HitEndTimes = new int[] { 6410 }, FileName = "MCD0314.ani" } },
        { (6, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2580 }, HitEndTimes = new int[] { 3530 }, FileName = "MCD0401.ani" } },
        { (6, 402, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2240 }, HitEndTimes = new int[] { 3480 }, FileName = "MCD0402.ani" } },
        { (6, 403, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2460 }, HitEndTimes = new int[] { 2930 }, FileName = "MCD0403.ani" } },
        { (6, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCD0900.ani" } },
        { (5, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 70, 2380 }, HitEndTimes = new int[] { 80, 2440 }, FileName = "MCE0103.ani" } },
        { (5, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 110, 1990 }, HitEndTimes = new int[] { 120, 2220 }, FileName = "MCE0104.ani" } },
        { (5, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2010 }, HitEndTimes = new int[] { 90, 2110 }, FileName = "MCE0105.ani" } },
        { (5, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2390 }, HitEndTimes = new int[] { 2630 }, FileName = "MCE0112.ani" } },
        { (5, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1070 }, HitEndTimes = new int[] { 1660 }, FileName = "MCE0113.ani" } },
        { (5, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2010 }, HitEndTimes = new int[] { 81, 2011 }, FileName = "MCE0121.ani" } },
        { (5, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCE0124.ani" } },
        { (5, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2010 }, HitEndTimes = new int[] { 81, 2011 }, FileName = "MCE0125.ani" } },
        { (5, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4090 }, HitEndTimes = new int[] { 5040 }, FileName = "MCE0126.ani" } },
        { (5, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5420 }, HitEndTimes = new int[] { 6190 }, FileName = "MCE0127.ani" } },
        { (5, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCE0142.ani" } },
        { (5, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 30, 2040 }, HitEndTimes = new int[] { 240, 2440 }, FileName = "MCE0143.ani" } },
        { (5, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCE0145.ani" } },
        { (5, 163, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8060 }, HitEndTimes = new int[] { 11840 }, FileName = "MCE0163.ani" } },
        { (5, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCE0166.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6070 }, HitEndTimes = new int[] { 7400 }, FileName = "MCE0202.ani" } },
        { (5, 206, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5480 }, HitEndTimes = new int[] { 10390 }, FileName = "MCE0206.ani" } },
        { (5, 207, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 6110 }, FileName = "MCE0207.ani" } },
        { (5, 208, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 13690 }, HitEndTimes = new int[] { 14110 }, FileName = "MCE0208.ani" } },
        { (5, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 830, 8640 }, HitEndTimes = new int[] { 1410, 9020 }, FileName = "MCE0210.ani" } },
        { (5, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8580 }, HitEndTimes = new int[] { 1100, 9150 }, FileName = "MCE0211.ani" } },
        { (5, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8960 }, HitEndTimes = new int[] { 1020, 9340 }, FileName = "MCE0212.ani" } },
        { (5, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 7940 }, FileName = "MCE0314.ani" } },
        { (5, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4180 }, HitEndTimes = new int[] { 5400 }, FileName = "MCE0401.ani" } },
        { (5, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCE0900.ani" } },
        { (13, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 70, 2380 }, HitEndTimes = new int[] { 80, 2440 }, FileName = "MCG0103.ani" } },
        { (13, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 110, 1990 }, HitEndTimes = new int[] { 120, 2220 }, FileName = "MCG0104.ani" } },
        { (13, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2010 }, HitEndTimes = new int[] { 90, 2110 }, FileName = "MCG0105.ani" } },
        { (13, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2390 }, HitEndTimes = new int[] { 2630 }, FileName = "MCG0112.ani" } },
        { (13, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1070 }, HitEndTimes = new int[] { 1660 }, FileName = "MCG0113.ani" } },
        { (13, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2010 }, HitEndTimes = new int[] { 81, 2011 }, FileName = "MCG0121.ani" } },
        { (13, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCG0124.ani" } },
        { (13, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2010 }, HitEndTimes = new int[] { 81, 2011 }, FileName = "MCG0125.ani" } },
        { (13, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4090 }, HitEndTimes = new int[] { 5040 }, FileName = "MCG0126.ani" } },
        { (13, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5420 }, HitEndTimes = new int[] { 6190 }, FileName = "MCG0127.ani" } },
        { (13, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCG0142.ani" } },
        { (13, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 30, 2040 }, HitEndTimes = new int[] { 240, 2440 }, FileName = "MCG0143.ani" } },
        { (13, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCG0145.ani" } },
        { (13, 163, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8060 }, HitEndTimes = new int[] { 11840 }, FileName = "MCG0163.ani" } },
        { (13, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCG0166.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6070 }, HitEndTimes = new int[] { 7400 }, FileName = "MCG0202.ani" } },
        { (13, 203, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3080, 5280 }, HitEndTimes = new int[] { 4100, 6500 }, FileName = "MCG0203.ani" } },
        { (13, 204, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3080, 5280 }, HitEndTimes = new int[] { 4100, 6500 }, FileName = "MCG0204.ani" } },
        { (13, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5700 }, HitEndTimes = new int[] { 6500 }, FileName = "MCG0205.ani" } },
        { (13, 206, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5480 }, HitEndTimes = new int[] { 10390 }, FileName = "MCG0206.ani" } },
        { (13, 207, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 6110 }, FileName = "MCG0207.ani" } },
        { (13, 208, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 13690 }, HitEndTimes = new int[] { 14110 }, FileName = "MCG0208.ani" } },
        { (13, 209, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5430 }, HitEndTimes = new int[] { 7230 }, FileName = "MCG0209.ani" } },
        { (13, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 830, 8640 }, HitEndTimes = new int[] { 1410, 9020 }, FileName = "MCG0210.ani" } },
        { (13, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8580 }, HitEndTimes = new int[] { 1100, 9150 }, FileName = "MCG0211.ani" } },
        { (13, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8960 }, HitEndTimes = new int[] { 1020, 9340 }, FileName = "MCG0212.ani" } },
        { (13, 213, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3980 }, HitEndTimes = new int[] { 5000 }, FileName = "MCG0213.ani" } },
        { (13, 214, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6000 }, HitEndTimes = new int[] { 7000 }, FileName = "MCG0214.ani" } },
        { (13, 215, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3780 }, HitEndTimes = new int[] { 4120 }, FileName = "MCG0215.ani" } },
        { (13, 216, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4900 }, HitEndTimes = new int[] { 5300 }, FileName = "MCG0216.ani" } },
        { (13, 217, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4780, 5010, 5500 }, HitEndTimes = new int[] { 5010, 5310, 5900 }, FileName = "MCG0217.ani" } },
        { (13, 218, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4950 }, HitEndTimes = new int[] { 7330 }, FileName = "MCG0218.ani" } },
        { (13, 219, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4000 }, HitEndTimes = new int[] { 4500 }, FileName = "MCG0219.ani" } },
        { (13, 220, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3800 }, HitEndTimes = new int[] { 4690 }, FileName = "MCG0220.ani" } },
        { (13, 221, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 3500, 4200, 4900, 5600, 6200 }, HitEndTimes = new int[] { 4200, 4900, 5600, 6200, 7000 }, FileName = "MCG0221.ani" } },
        { (13, 222, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2500 }, HitEndTimes = new int[] { 3150 }, FileName = "MCG0222.ani" } },
        { (13, 223, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 5200, 5500, 6000, 6500 }, HitEndTimes = new int[] { 5900, 6200, 6600, 8000 }, FileName = "MCG0223.ani" } },
        { (13, 300, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5000 }, HitEndTimes = new int[] { 5500 }, FileName = "MCG0300.ani" } },
        { (13, 301, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4600 }, HitEndTimes = new int[] { 6500 }, FileName = "MCG0301.ani" } },
        { (13, 302, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5000 }, HitEndTimes = new int[] { 6000 }, FileName = "MCG0302.ani" } },
        { (13, 303, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3800 }, HitEndTimes = new int[] { 4500 }, FileName = "MCG0303.ani" } },
        { (13, 304, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5000 }, HitEndTimes = new int[] { 6000 }, FileName = "MCG0304.ani" } },
        { (13, 305, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3800 }, HitEndTimes = new int[] { 4800 }, FileName = "MCG0305.ani" } },
        { (13, 306, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5090 }, HitEndTimes = new int[] { 6000 }, FileName = "MCG0306.ani" } },
        { (13, 307, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 3510, 4010, 4510, 5010, 5510 }, HitEndTimes = new int[] { 4510, 5010, 5510, 6010, 6800 }, FileName = "MCG0307.ani" } },
        { (13, 308, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2470, 3640, 6160 }, HitEndTimes = new int[] { 3400, 4600, 7100 }, FileName = "MCG0308.ani" } },
        { (13, 310, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3880 }, HitEndTimes = new int[] { 4600 }, FileName = "MCG0310.ani" } },
        { (13, 311, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5240 }, HitEndTimes = new int[] { 6200 }, FileName = "MCG0311.ani" } },
        { (13, 312, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6110 }, HitEndTimes = new int[] { 7100 }, FileName = "MCG0312.ani" } },
        { (13, 313, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4000, 4600, 5200 }, HitEndTimes = new int[] { 4600, 5200, 6210 }, FileName = "MCG0313.ani" } },
        { (13, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 7940 }, FileName = "MCG0314.ani" } },
        { (13, 315, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4500 }, HitEndTimes = new int[] { 6000 }, FileName = "MCG0315.ani" } },
        { (13, 316, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5090, 5500, 6000 }, HitEndTimes = new int[] { 6000, 6500, 7000 }, FileName = "MCG0316.ani" } },
        { (13, 317, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4710 }, HitEndTimes = new int[] { 5700 }, FileName = "MCG0317.ani" } },
        { (13, 318, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4510 }, HitEndTimes = new int[] { 5500 }, FileName = "MCG0318.ani" } },
        { (13, 341, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5800 }, HitEndTimes = new int[] { 5950 }, FileName = "MCG0341.ani" } },
        { (13, 342, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5450 }, HitEndTimes = new int[] { 5600 }, FileName = "MCG0342.ani" } },
        { (13, 343, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5450 }, HitEndTimes = new int[] { 5600 }, FileName = "MCG0343.ani" } },
        { (13, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4180 }, HitEndTimes = new int[] { 5400 }, FileName = "MCG0401.ani" } },
        { (13, 404, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 5000, 5400, 5800, 6200 }, HitEndTimes = new int[] { 5300, 5700, 6100, 6500 }, FileName = "MCG0404.ani" } },
        { (13, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCG0900.ani" } },
        { (9, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 2440 }, HitEndTimes = new int[] { 140, 2550 }, FileName = "MCH0103.ani" } },
        { (9, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 160, 2090 }, HitEndTimes = new int[] { 170, 2110 }, FileName = "MCH0104.ani" } },
        { (9, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 110, 2040 }, HitEndTimes = new int[] { 120, 2110 }, FileName = "MCH0105.ani" } },
        { (9, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 110, 2040 }, HitEndTimes = new int[] { 120, 2110 }, FileName = "MCH0105_type2.ani" } },
        { (9, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2350 }, HitEndTimes = new int[] { 2700 }, FileName = "MCH0112.ani" } },
        { (9, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1200 }, HitEndTimes = new int[] { 2200 }, FileName = "MCH0113.ani" } },
        { (9, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2070 }, HitEndTimes = new int[] { 81, 2071 }, FileName = "MCH0121.ani" } },
        { (9, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2070 }, HitEndTimes = new int[] { 81, 2071 }, FileName = "MCH0121_type2.ani" } },
        { (9, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCH0124.ani" } },
        { (9, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2070 }, HitEndTimes = new int[] { 81, 2071 }, FileName = "MCH0125.ani" } },
        { (9, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2110 }, HitEndTimes = new int[] { 2510 }, FileName = "MCH0126.ani" } },
        { (9, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2630 }, HitEndTimes = new int[] { 3230 }, FileName = "MCH0127.ani" } },
        { (9, 128, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1780, 4010, 5910 }, HitEndTimes = new int[] { 1840, 4070, 6520 }, FileName = "MCH0128.ani" } },
        { (9, 129, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1730, 3270, 5550 }, HitEndTimes = new int[] { 1880, 3420, 5850 }, FileName = "MCH0129.ani" } },
        { (9, 130, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2320, 5590, 7700 }, HitEndTimes = new int[] { 2590, 5790, 7770 }, FileName = "MCH0130.ani" } },
        { (9, 131, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2450, 4630, 8240 }, HitEndTimes = new int[] { 3130, 5250, 9060 }, FileName = "MCH0131.ani" } },
        { (9, 132, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2450, 4320, 8890 }, HitEndTimes = new int[] { 2710, 4740, 9560 }, FileName = "MCH0132.ani" } },
        { (9, 133, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1050, 5260, 7690 }, HitEndTimes = new int[] { 1710, 5720, 8010 }, FileName = "MCH0133.ani" } },
        { (9, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3630 }, HitEndTimes = new int[] { 4750 }, FileName = "MCH0134.ani" } },
        { (9, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCH0142.ani" } },
        { (9, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 50, 2070 }, HitEndTimes = new int[] { 160, 2230 }, FileName = "MCH0143.ani" } },
        { (9, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 50, 2070 }, HitEndTimes = new int[] { 160, 2230 }, FileName = "MCH0143_type2.ani" } },
        { (9, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCH0145.ani" } },
        { (9, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCH0145_type2.ani" } },
        { (9, 163, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7430 }, HitEndTimes = new int[] { 10450 }, FileName = "MCH0163.ani" } },
        { (9, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCH0166.ani" } },
        { (9, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4770 }, HitEndTimes = new int[] { 5170 }, FileName = "MCH0201.ani" } },
        { (9, 202, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 4650, 5550, 6400, 7200 }, HitEndTimes = new int[] { 4950, 5850, 6700, 7500 }, FileName = "MCH0202.ani" } },
        { (9, 203, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1440, 2720, 3360, 4800, 6100 }, HitEndTimes = new int[] { 1840, 3120, 3760, 5400, 6500 }, FileName = "MCH0203.ani" } },
        { (9, 204, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1440, 2720, 3360, 4800, 6100 }, HitEndTimes = new int[] { 1840, 3120, 3760, 5400, 6500 }, FileName = "MCH0204.ani" } },
        { (9, 205, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 7350, 8300, 9300 }, HitEndTimes = new int[] { 7700, 8630, 9650 }, FileName = "MCH0205.ani" } },
        { (9, 206, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1567, 2964, 4243 }, HitEndTimes = new int[] { 1802, 3199, 4556 }, FileName = "MCH0206.ani" } },
        { (9, 207, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 3721, 4348, 4974, 5640 }, HitEndTimes = new int[] { 3956, 4583, 5248, 5914 }, FileName = "MCH0207.ani" } },
        { (9, 208, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7500 }, HitEndTimes = new int[] { 7940 }, FileName = "MCH0208.ani" } },
        { (9, 209, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1797, 2765, 4458 }, HitEndTimes = new int[] { 2074, 2993, 4735 }, FileName = "MCH0209.ani" } },
        { (9, 210, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 3060, 4340, 5700, 6980 }, HitEndTimes = new int[] { 2070, 3420, 4700, 6130, 7340 }, FileName = "MCH0210.ani" } },
        { (9, 211, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 4400, 10000, 10900, 12500 }, HitEndTimes = new int[] { 2070, 5050, 10400, 11300, 12950 }, FileName = "MCH0211.ani" } },
        { (9, 212, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2420, 4210, 6260 }, HitEndTimes = new int[] { 2850, 4650, 6630 }, FileName = "MCH0212.ani" } },
        { (9, 213, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2450 }, HitEndTimes = new int[] { 2850 }, FileName = "MCH0213.ani" } },
        { (9, 214, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3400 }, HitEndTimes = new int[] { 3900 }, FileName = "MCH0214.ani" } },
        { (9, 215, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2300 }, HitEndTimes = new int[] { 2760 }, FileName = "MCH0215.ani" } },
        { (9, 216, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2100, 3700 }, HitEndTimes = new int[] { 2450, 4200 }, FileName = "MCH0216.ani" } },
        { (9, 217, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4250, 5000 }, HitEndTimes = new int[] { 4600, 5400 }, FileName = "MCH0217.ani" } },
        { (9, 218, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1450 }, HitEndTimes = new int[] { 1800 }, FileName = "MCH0218.ani" } },
        { (9, 219, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2450 }, FileName = "MCH0219.ani" } },
        { (9, 300, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6200 }, HitEndTimes = new int[] { 6750 }, FileName = "MCH0300.ani" } },
        { (9, 301, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4850, 6410, 8800 }, HitEndTimes = new int[] { 5160, 6800, 9200 }, FileName = "MCH0301.ani" } },
        { (9, 302, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2520, 3320, 4120, 4850, 5700 }, HitEndTimes = new int[] { 2820, 3620, 4430, 5160, 6200 }, FileName = "MCH0302.ani" } },
        { (9, 303, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5050 }, HitEndTimes = new int[] { 5690 }, FileName = "MCH0303.ani" } },
        { (9, 304, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4930, 6500 }, HitEndTimes = new int[] { 5300, 6900 }, FileName = "MCH0304.ani" } },
        { (9, 305, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 7280, 9300 }, HitEndTimes = new int[] { 7900, 9940 }, FileName = "MCH0305.ani" } },
        { (9, 314, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3650, 4800, 6530 }, HitEndTimes = new int[] { 3900, 4990, 7040 }, FileName = "MCH0314.ani" } },
        { (9, 319, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3615 }, HitEndTimes = new int[] { 4218 }, FileName = "MCH0319.ani" } },
        { (9, 320, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2795, 3447, 4756 }, HitEndTimes = new int[] { 3152, 3785, 5433 }, FileName = "MCH0320.ani" } },
        { (9, 326, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4244 }, HitEndTimes = new int[] { 4899 }, FileName = "MCH0326.ani" } },
        { (9, 326, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5960 }, HitEndTimes = new int[] { 6880 }, FileName = "MCH0326_1.ani" } },
        { (9, 327, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2795, 3447 }, HitEndTimes = new int[] { 3152, 3785 }, FileName = "MCH0327.ani" } },
        { (9, 327, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4460, 5500, 7590 }, HitEndTimes = new int[] { 5030, 6040, 8670 }, FileName = "MCH0327_1.ani" } },
        { (9, 330, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6060 }, HitEndTimes = new int[] { 7370 }, FileName = "MCH0330.ani" } },
        { (9, 331, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5190 }, HitEndTimes = new int[] { 6550 }, FileName = "MCH0331.ani" } },
        { (9, 341, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5450 }, HitEndTimes = new int[] { 5600 }, FileName = "MCH0341.ani" } },
        { (9, 342, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4950, 5100 }, HitEndTimes = new int[] { 5100, 5250 }, FileName = "MCH0342.ani" } },
        { (9, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3170 }, HitEndTimes = new int[] { 3700 }, FileName = "MCH0401.ani" } },
        { (9, 402, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3070 }, HitEndTimes = new int[] { 3470 }, FileName = "MCH0402.ani" } },
        { (9, 403, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2770 }, HitEndTimes = new int[] { 3040 }, FileName = "MCH0403.ani" } },
        { (9, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCH0900.ani" } },
        { (1, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCK0103.ani" } },
        { (1, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 190, 2090 }, HitEndTimes = new int[] { 200, 2100 }, FileName = "MCK0104.ani" } },
        { (1, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 210, 2070 }, HitEndTimes = new int[] { 220, 2100 }, FileName = "MCK0105.ani" } },
        { (1, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2390 }, HitEndTimes = new int[] { 2950 }, FileName = "MCK0112.ani" } },
        { (1, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1090 }, HitEndTimes = new int[] { 1590 }, FileName = "MCK0113.ani" } },
        { (1, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 1960 }, HitEndTimes = new int[] { 81, 1961 }, FileName = "MCK0121.ani" } },
        { (1, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCK0124.ani" } },
        { (1, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2010 }, HitEndTimes = new int[] { 81, 2011 }, FileName = "MCK0125.ani" } },
        { (1, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCK0126.ani" } },
        { (1, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2640 }, HitEndTimes = new int[] { 3670 }, FileName = "MCK0127.ani" } },
        { (1, 128, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1770, 4840, 7830 }, HitEndTimes = new int[] { 2380, 5450, 8180 }, FileName = "MCK0128.ani" } },
        { (1, 129, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1620, 3710, 7120 }, HitEndTimes = new int[] { 2040, 4730, 7720 }, FileName = "MCK0129.ani" } },
        { (1, 130, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1780, 5130, 8770 }, HitEndTimes = new int[] { 2530, 5570, 9590 }, FileName = "MCK0130.ani" } },
        { (1, 131, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2330, 4910, 7420 }, HitEndTimes = new int[] { 2990, 5690, 8080 }, FileName = "MCK0131.ani" } },
        { (1, 132, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3060, 6280, 10060 }, HitEndTimes = new int[] { 3620, 6680, 10470 }, FileName = "MCK0132.ani" } },
        { (1, 133, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1010, 4680, 8570 }, HitEndTimes = new int[] { 1220, 5190, 8790 }, FileName = "MCK0133.ani" } },
        { (1, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4750 }, HitEndTimes = new int[] { 5600 }, FileName = "MCK0134.ani" } },
        { (1, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCK0142.ani" } },
        { (1, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2040 }, HitEndTimes = new int[] { 160, 2250 }, FileName = "MCK0143.ani" } },
        { (1, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCK0145.ani" } },
        { (1, 163, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7430 }, HitEndTimes = new int[] { 10960 }, FileName = "MCK0163.ani" } },
        { (1, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2080 }, HitEndTimes = new int[] { 3170 }, FileName = "MCK0166.ani" } },
        { (1, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2490 }, HitEndTimes = new int[] { 6650 }, FileName = "MCK0201.ani" } },
        { (1, 201, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2750, 7040 }, HitEndTimes = new int[] { 3650, 7360 }, FileName = "MCK0201.ani.bak" } },
        { (1, 202, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2750, 7040 }, HitEndTimes = new int[] { 3650, 7360 }, FileName = "MCK0202.ani" } },
        { (1, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2750 }, HitEndTimes = new int[] { 3650 }, FileName = "MCK0202.ani.bak" } },
        { (1, 203, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2250, 6130 }, HitEndTimes = new int[] { 3060, 6880 }, FileName = "MCK0203.ani" } },
        { (1, 204, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4550, 8170 }, HitEndTimes = new int[] { 4990, 8740 }, FileName = "MCK0204.ani" } },
        { (1, 208, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 9170 }, HitEndTimes = new int[] { 10320 }, FileName = "MCK0208.ani" } },
        { (1, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 190, 6590 }, HitEndTimes = new int[] { 450, 7490 }, FileName = "MCK0210.ani" } },
        { (1, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1980, 6720 }, HitEndTimes = new int[] { 2560, 7170 }, FileName = "MCK0211.ani" } },
        { (1, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1450, 7490 }, HitEndTimes = new int[] { 1800, 8190 }, FileName = "MCK0212.ani" } },
        { (1, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6720 }, HitEndTimes = new int[] { 7040 }, FileName = "MCK0314.ani" } },
        { (1, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3200 }, HitEndTimes = new int[] { 3440 }, FileName = "MCK0401.ani" } },
        { (1, 402, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 830, 3470 }, HitEndTimes = new int[] { 920, 4200 }, FileName = "MCK0402.ani" } },
        { (1, 403, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1120, 3470 }, HitEndTimes = new int[] { 1260, 4000 }, FileName = "MCK0403.ani" } },
        { (1, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCK0900.ani" } },
        { (7, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCM0103.ani" } },
        { (7, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 190, 2090 }, HitEndTimes = new int[] { 200, 2100 }, FileName = "MCM0104.ani" } },
        { (7, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 210, 2070 }, HitEndTimes = new int[] { 220, 2100 }, FileName = "MCM0105.ani" } },
        { (7, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2390 }, HitEndTimes = new int[] { 2600 }, FileName = "MCM0112.ani" } },
        { (7, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1090 }, HitEndTimes = new int[] { 1450 }, FileName = "MCM0113.ani" } },
        { (7, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 1960 }, HitEndTimes = new int[] { 81, 1961 }, FileName = "MCM0121.ani" } },
        { (7, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCM0124.ani" } },
        { (7, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCM0124.ani2" } },
        { (7, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2010 }, HitEndTimes = new int[] { 81, 2011 }, FileName = "MCM0125.ani" } },
        { (7, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2350 }, HitEndTimes = new int[] { 2540 }, FileName = "MCM0126.ani" } },
        { (7, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2970 }, HitEndTimes = new int[] { 3340 }, FileName = "MCM0127.ani" } },
        { (7, 128, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2750, 4100, 5630 }, HitEndTimes = new int[] { 3070, 4480, 6530 }, FileName = "MCM0128.ani" } },
        { (7, 129, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2750, 4100, 5630 }, HitEndTimes = new int[] { 3070, 4480, 6530 }, FileName = "MCM0129.ani" } },
        { (7, 130, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2750, 4100, 5630 }, HitEndTimes = new int[] { 3070, 4480, 6530 }, FileName = "MCM0130.ani" } },
        { (7, 131, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2750, 4100, 5630 }, HitEndTimes = new int[] { 3070, 4480, 6530 }, FileName = "MCM0131.ani" } },
        { (7, 132, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2750, 4100, 5630 }, HitEndTimes = new int[] { 3070, 4480, 6530 }, FileName = "MCM0132.ani" } },
        { (7, 133, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2750, 4100, 5630 }, HitEndTimes = new int[] { 3070, 4480, 6530 }, FileName = "MCM0133.ani" } },
        { (7, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2970 }, HitEndTimes = new int[] { 3340 }, FileName = "MCM0134.ani" } },
        { (7, 136, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2970 }, HitEndTimes = new int[] { 3340 }, FileName = "MCM0136.ani" } },
        { (7, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1340 }, HitEndTimes = new int[] { 1640 }, FileName = "MCM0142.ani" } },
        { (7, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1340 }, HitEndTimes = new int[] { 1640 }, FileName = "MCM0142.ani2" } },
        { (7, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2040 }, HitEndTimes = new int[] { 160, 2250 }, FileName = "MCM0143.ani" } },
        { (7, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCM0145.ani" } },
        { (7, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCM0166.ani" } },
        { (7, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "MCM0201.ani" } },
        { (7, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 7360 }, FileName = "MCM0202.ani" } },
        { (7, 203, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 7360 }, FileName = "MCM0203.ani" } },
        { (7, 204, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 7360 }, FileName = "MCM0204.ani" } },
        { (7, 206, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5480 }, HitEndTimes = new int[] { 10390 }, FileName = "MCM0206.ani" } },
        { (7, 207, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 6110 }, FileName = "MCM0207.ani" } },
        { (7, 208, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 13690 }, HitEndTimes = new int[] { 14110 }, FileName = "MCM0208.ani" } },
        { (7, 210, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4860 }, HitEndTimes = new int[] { 5440 }, FileName = "MCM0210.ani" } },
        { (7, 211, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6020 }, HitEndTimes = new int[] { 7100 }, FileName = "MCM0211.ani" } },
        { (7, 212, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5630 }, HitEndTimes = new int[] { 5700 }, FileName = "MCM0212.ani" } },
        { (7, 301, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3300 }, HitEndTimes = new int[] { 3830 }, FileName = "MCM0301.ani" } },
        { (7, 305, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 7360 }, FileName = "MCM0305.ani" } },
        { (7, 306, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 7360 }, FileName = "MCM0306.ani" } },
        { (7, 307, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 7360 }, FileName = "MCM0307.ani" } },
        { (7, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4740 }, HitEndTimes = new int[] { 4860 }, FileName = "MCM0314.ani" } },
        { (7, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4180 }, HitEndTimes = new int[] { 5400 }, FileName = "MCM0401.ani" } },
        { (7, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCM0900.ani2" } },
        { (12, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2350 }, HitEndTimes = new int[] { 2530 }, FileName = "MCO0112.ani" } },
        { (12, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1140 }, HitEndTimes = new int[] { 1480 }, FileName = "MCO0113.ani" } },
        { (12, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 2090 }, HitEndTimes = new int[] { 240, 2250 }, FileName = "MCO0121.ani" } },
        { (12, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCO0124.ani" } },
        { (12, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 2090 }, HitEndTimes = new int[] { 300, 2310 }, FileName = "MCO0125.ani" } },
        { (12, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3840 }, HitEndTimes = new int[] { 4000 }, FileName = "MCO0126.ani" } },
        { (12, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3730 }, HitEndTimes = new int[] { 3900 }, FileName = "MCO0127.ani" } },
        { (12, 128, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2970, 4980, 8400 }, HitEndTimes = new int[] { 3640, 5350, 9510 }, FileName = "MCO0128.ani" } },
        { (12, 129, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3370, 6300, 10830 }, HitEndTimes = new int[] { 3820, 6920, 11720 }, FileName = "MCO0129.ani" } },
        { (12, 130, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2220, 5240, 10740 }, HitEndTimes = new int[] { 3020, 5590, 11100 }, FileName = "MCO0130.ani" } },
        { (12, 131, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3420, 7600, 12060 }, HitEndTimes = new int[] { 3990, 8170, 12920 }, FileName = "MCO0131.ani" } },
        { (12, 132, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2580, 5350, 11890 }, HitEndTimes = new int[] { 3570, 7230, 12490 }, FileName = "MCO0132.ani" } },
        { (12, 133, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 920, 4010, 6040 }, HitEndTimes = new int[] { 1120, 4470, 6310 }, FileName = "MCO0133.ani" } },
        { (12, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3140 }, HitEndTimes = new int[] { 4000 }, FileName = "MCO0134.ani" } },
        { (12, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCO0142.ani" } },
        { (12, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 30, 2040 }, HitEndTimes = new int[] { 130, 2170 }, FileName = "MCO0143.ani" } },
        { (12, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCO0145.ani" } },
        { (12, 163, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 10450 }, HitEndTimes = new int[] { 12220 }, FileName = "MCO0163.ani" } },
        { (12, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCO0166.ani" } },
        { (12, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8550 }, HitEndTimes = new int[] { 10030 }, FileName = "MCO0201.ani" } },
        { (12, 202, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2590, 8320 }, HitEndTimes = new int[] { 2900, 8540 }, FileName = "MCO0202.ani" } },
        { (12, 203, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 6240, 7510, 9440 }, HitEndTimes = new int[] { 6760, 8030, 10260 }, FileName = "MCO0203.ani" } },
        { (12, 206, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1680, 5810 }, HitEndTimes = new int[] { 2390, 7180 }, FileName = "MCO0206.ani" } },
        { (12, 207, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6050 }, HitEndTimes = new int[] { 6950 }, FileName = "MCO0207.ani" } },
        { (12, 208, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 4930 }, HitEndTimes = new int[] { 2620, 5950 }, FileName = "MCO0208.ani" } },
        { (12, 209, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2230, 6040 }, HitEndTimes = new int[] { 2690, 6310 }, FileName = "MCO0209.ani" } },
        { (12, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1150, 7550 }, HitEndTimes = new int[] { 1280, 8130 }, FileName = "MCO0210.ani" } },
        { (12, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 7680 }, HitEndTimes = new int[] { 1730, 8060 }, FileName = "MCO0211.ani" } },
        { (12, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 7420 }, HitEndTimes = new int[] { 1340, 7870 }, FileName = "MCO0212.ani" } },
        { (12, 300, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2510, 3770, 7070 }, HitEndTimes = new int[] { 3240, 4690, 7600 }, FileName = "MCO0300.ani" } },
        { (12, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 6980 }, FileName = "MCO0314.ani" } },
        { (12, 320, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6270 }, HitEndTimes = new int[] { 6590 }, FileName = "MCO0320.ani" } },
        { (12, 326, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6500 }, HitEndTimes = new int[] { 7740 }, FileName = "MCO0326.ani" } },
        { (12, 327, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5180 }, HitEndTimes = new int[] { 6820 }, FileName = "MCO0327.ani" } },
        { (12, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2820 }, HitEndTimes = new int[] { 3270 }, FileName = "MCO0401.ani" } },
        { (12, 402, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2810 }, HitEndTimes = new int[] { 3200 }, FileName = "MCO0402.ani" } },
        { (12, 403, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 890, 2050, 3700 }, HitEndTimes = new int[] { 1260, 2410, 4290 }, FileName = "MCO0403.ani" } },
        { (12, 406, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2150 }, HitEndTimes = new int[] { 2440 }, FileName = "MCO0406.ani" } },
        { (12, 407, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1880 }, HitEndTimes = new int[] { 2080 }, FileName = "MCO0407.ani" } },
        { (12, 408, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1390, 3070 }, HitEndTimes = new int[] { 1520, 3240 }, FileName = "MCO0408.ani" } },
        { (12, 409, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2840 }, HitEndTimes = new int[] { 3010 }, FileName = "MCO0409.ani" } },
        { (12, 500, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 630, 1850, 3140 }, HitEndTimes = new int[] { 790, 2350, 3400 }, FileName = "MCO0500.ani" } },
        { (12, 501, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 860, 3140 }, HitEndTimes = new int[] { 1260, 3400 }, FileName = "MCO0501.ani" } },
        { (12, 502, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 810, 3470 }, HitEndTimes = new int[] { 2280, 4880 }, FileName = "MCO0502.ani" } },
        { (12, 503, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3340 }, HitEndTimes = new int[] { 4530 }, FileName = "MCO0503.ani" } },
        { (12, 504, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1160, 3240 }, HitEndTimes = new int[] { 2770, 4660 }, FileName = "MCO0504.ani" } },
        { (12, 505, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 760, 2770 }, HitEndTimes = new int[] { 2210, 4330 }, FileName = "MCO0505.ani" } },
        { (12, 506, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 920, 3860 }, HitEndTimes = new int[] { 3200, 4790 }, FileName = "MCO0506.ani" } },
        { (12, 507, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1060, 2150 }, HitEndTimes = new int[] { 1880, 4060 }, FileName = "MCO0507.ani" } },
        { (12, 508, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 370, 2100 }, HitEndTimes = new int[] { 680, 2340 }, FileName = "MCO0508.ani" } },
        { (12, 509, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 310, 1830 }, HitEndTimes = new int[] { 640, 2240 }, FileName = "MCO0509.ani" } },
        { (12, 510, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 270, 1560, 3020 }, HitEndTimes = new int[] { 510, 1930, 3420 }, FileName = "MCO0510.ani" } },
        { (12, 511, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 630, 3630 }, HitEndTimes = new int[] { 2380, 4560 }, FileName = "MCO0511.ani" } },
        { (12, 512, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 430, 1980 }, HitEndTimes = new int[] { 1780, 4100 }, FileName = "MCO0512.ani" } },
        { (12, 513, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 660, 2970 }, HitEndTimes = new int[] { 2080, 4620 }, FileName = "MCO0513.ani" } },
        { (12, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCO0900.ani" } },
        { (2, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 2440 }, HitEndTimes = new int[] { 140, 2550 }, FileName = "MCS0103.ani" } },
        { (2, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 160, 2090 }, HitEndTimes = new int[] { 170, 2110 }, FileName = "MCS0104.ani" } },
        { (2, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 110, 2040 }, HitEndTimes = new int[] { 120, 2110 }, FileName = "MCS0105.ani" } },
        { (2, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2350 }, HitEndTimes = new int[] { 2700 }, FileName = "MCS0112.ani" } },
        { (2, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1200 }, HitEndTimes = new int[] { 2200 }, FileName = "MCS0113.ani" } },
        { (2, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2070 }, HitEndTimes = new int[] { 81, 2071 }, FileName = "MCS0121.ani" } },
        { (2, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCS0124.ani" } },
        { (2, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2070 }, HitEndTimes = new int[] { 81, 2071 }, FileName = "MCS0125.ani" } },
        { (2, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2110 }, HitEndTimes = new int[] { 2510 }, FileName = "MCS0126.ani" } },
        { (2, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2630 }, HitEndTimes = new int[] { 3230 }, FileName = "MCS0127.ani" } },
        { (2, 128, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1780, 4010, 5910 }, HitEndTimes = new int[] { 1840, 4070, 6520 }, FileName = "MCS0128.ani" } },
        { (2, 129, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1730, 3270, 5550 }, HitEndTimes = new int[] { 1880, 3420, 5850 }, FileName = "MCS0129.ani" } },
        { (2, 130, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2320, 5590, 7700 }, HitEndTimes = new int[] { 2590, 5790, 7770 }, FileName = "MCS0130.ani" } },
        { (2, 131, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2450, 4630, 8240 }, HitEndTimes = new int[] { 3130, 5250, 9060 }, FileName = "MCS0131.ani" } },
        { (2, 132, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2450, 4320, 8890 }, HitEndTimes = new int[] { 2710, 4740, 9560 }, FileName = "MCS0132.ani" } },
        { (2, 133, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1050, 5260, 7690 }, HitEndTimes = new int[] { 1710, 5720, 8010 }, FileName = "MCS0133.ani" } },
        { (2, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3630 }, HitEndTimes = new int[] { 4750 }, FileName = "MCS0134.ani" } },
        { (2, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCS0142.ani" } },
        { (2, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 50, 2070 }, HitEndTimes = new int[] { 160, 2230 }, FileName = "MCS0143.ani" } },
        { (2, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCS0145.ani" } },
        { (2, 163, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7430 }, HitEndTimes = new int[] { 10450 }, FileName = "MCS0163.ani" } },
        { (2, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCS0166.ani" } },
        { (2, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8240 }, HitEndTimes = new int[] { 8710 }, FileName = "MCS0201.ani" } },
        { (2, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5900 }, HitEndTimes = new int[] { 7050 }, FileName = "MCS0202.ani" } },
        { (2, 203, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7090 }, HitEndTimes = new int[] { 8350 }, FileName = "MCS0203.ani" } },
        { (2, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCS0205.ani" } },
        { (2, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 8900 }, HitEndTimes = new int[] { 1410, 9660 }, FileName = "MCS0210.ani" } },
        { (2, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1110, 8000 }, HitEndTimes = new int[] { 1280, 8830 }, FileName = "MCS0211.ani" } },
        { (2, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2560, 8510 }, HitEndTimes = new int[] { 2880, 8960 }, FileName = "MCS0212.ani" } },
        { (2, 314, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3650, 4800, 6530 }, HitEndTimes = new int[] { 3900, 4990, 7040 }, FileName = "MCS0314.ani" } },
        { (2, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3170 }, HitEndTimes = new int[] { 3700 }, FileName = "MCS0401.ani" } },
        { (2, 402, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3070 }, HitEndTimes = new int[] { 3470 }, FileName = "MCS0402.ani" } },
        { (2, 403, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2770 }, HitEndTimes = new int[] { 3040 }, FileName = "MCS0403.ani" } },
        { (2, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCS0900.ani" } },
        { (11, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCU0103.ani" } },
        { (11, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 190, 2090 }, HitEndTimes = new int[] { 200, 2100 }, FileName = "MCU0104.ani" } },
        { (11, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 110, 2040 }, HitEndTimes = new int[] { 120, 2110 }, FileName = "MCU0105.ani" } },
        { (11, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2350 }, HitEndTimes = new int[] { 2750 }, FileName = "MCU0112.ani" } },
        { (11, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1200 }, HitEndTimes = new int[] { 2200 }, FileName = "MCU0113.ani" } },
        { (11, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2070 }, HitEndTimes = new int[] { 81, 2071 }, FileName = "MCU0121.ani" } },
        { (11, 123, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 650 }, HitEndTimes = new int[] { 12220 }, FileName = "MCU0123.ani" } },
        { (11, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCU0124.ani" } },
        { (11, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2070 }, HitEndTimes = new int[] { 81, 2071 }, FileName = "MCU0125.ani" } },
        { (11, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1770 }, HitEndTimes = new int[] { 5850 }, FileName = "MCU0126.ani" } },
        { (11, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1770 }, HitEndTimes = new int[] { 5850 }, FileName = "MCU0127.ani" } },
        { (11, 128, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCU0128.ani" } },
        { (11, 129, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2110, 5050, 6740 }, HitEndTimes = new int[] { 4350, 6460, 7510 }, FileName = "MCU0129.ani" } },
        { (11, 130, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCU0130.ani" } },
        { (11, 131, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCU0131.ani" } },
        { (11, 132, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCU0132.ani" } },
        { (11, 133, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 7340, 11070 }, HitEndTimes = new int[] { 5710, 9560, 13640 }, FileName = "MCU0133.ani" } },
        { (11, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2740 }, HitEndTimes = new int[] { 7210 }, FileName = "MCU0134.ani" } },
        { (11, 136, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1610 }, HitEndTimes = new int[] { 2940 }, FileName = "MCU0136.ani" } },
        { (11, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCU0142.ani" } },
        { (11, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 50, 2070 }, HitEndTimes = new int[] { 160, 2230 }, FileName = "MCU0143.ani" } },
        { (11, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCU0145.ani" } },
        { (11, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCU0166.ani" } },
        { (11, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3880 }, HitEndTimes = new int[] { 6040 }, FileName = "MCU0202.ani" } },
        { (11, 203, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1450 }, HitEndTimes = new int[] { 4660 }, FileName = "MCU0203.ani" } },
        { (11, 204, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4400 }, HitEndTimes = new int[] { 6640 }, FileName = "MCU0204.ani" } },
        { (11, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1540 }, HitEndTimes = new int[] { 2460 }, FileName = "MCU0205.ani" } },
        { (11, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 830, 7550 }, HitEndTimes = new int[] { 1220, 8190 }, FileName = "MCU0210.ani" } },
        { (11, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 380, 8450 }, HitEndTimes = new int[] { 680, 9130 }, FileName = "MCU0211.ani" } },
        { (11, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 7810 }, HitEndTimes = new int[] { 380, 8700 }, FileName = "MCU0212.ani" } },
        { (11, 251, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1770 }, HitEndTimes = new int[] { 5850 }, FileName = "MCU0251.ani" } },
        { (11, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7040 }, HitEndTimes = new int[] { 8190 }, FileName = "MCU0314.ani" } },
        { (11, 315, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 650, 2300, 5000 }, HitEndTimes = new int[] { 1200, 2950, 5300 }, FileName = "MCU0315.ani" } },
        { (11, 317, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1770 }, HitEndTimes = new int[] { 5850 }, FileName = "MCU0317.ani" } },
        { (11, 402, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1220 }, HitEndTimes = new int[] { 2750 }, FileName = "MCU0402.ani" } },
        { (11, 403, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 270 }, HitEndTimes = new int[] { 2070 }, FileName = "MCU0403.ani" } },
        { (11, 404, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1830 }, HitEndTimes = new int[] { 3090 }, FileName = "MCU0404.ani" } },
        { (11, 415, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2700 }, HitEndTimes = new int[] { 5150 }, FileName = "MCU0415.ani" } },
        { (11, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCU0900.ani" } },
        { (8, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCV0103.ani" } },
        { (8, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 190, 2090 }, HitEndTimes = new int[] { 200, 2100 }, FileName = "MCV0104.ani" } },
        { (8, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 210, 2070 }, HitEndTimes = new int[] { 220, 2100 }, FileName = "MCV0105.ani" } },
        { (8, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2390 }, HitEndTimes = new int[] { 2950 }, FileName = "MCV0112.ani" } },
        { (8, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1090 }, HitEndTimes = new int[] { 1590 }, FileName = "MCV0113.ani" } },
        { (8, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 1960 }, HitEndTimes = new int[] { 81, 1961 }, FileName = "MCV0121.ani" } },
        { (8, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCV0124.ani" } },
        { (8, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2010 }, HitEndTimes = new int[] { 81, 2011 }, FileName = "MCV0125.ani" } },
        { (8, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCV0126.ani" } },
        { (8, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2640 }, HitEndTimes = new int[] { 3670 }, FileName = "MCV0127.ani" } },
        { (8, 128, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1770, 4840, 7830 }, HitEndTimes = new int[] { 2380, 5450, 8180 }, FileName = "MCV0128.ani" } },
        { (8, 129, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1620, 3710, 7120 }, HitEndTimes = new int[] { 2040, 4730, 7720 }, FileName = "MCV0129.ani" } },
        { (8, 130, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1780, 5130, 8770 }, HitEndTimes = new int[] { 2530, 5570, 9590 }, FileName = "MCV0130.ani" } },
        { (8, 131, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2330, 4910, 7420 }, HitEndTimes = new int[] { 2990, 5690, 8080 }, FileName = "MCV0131.ani" } },
        { (8, 132, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3060, 6280, 10060 }, HitEndTimes = new int[] { 3620, 6680, 10470 }, FileName = "MCV0132.ani" } },
        { (8, 133, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1010, 4680, 8570 }, HitEndTimes = new int[] { 1220, 5190, 8790 }, FileName = "MCV0133.ani" } },
        { (8, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4750 }, HitEndTimes = new int[] { 5600 }, FileName = "MCV0134.ani" } },
        { (8, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCV0142.ani" } },
        { (8, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2040 }, HitEndTimes = new int[] { 160, 2250 }, FileName = "MCV0143.ani" } },
        { (8, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCV0145.ani" } },
        { (8, 163, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7430 }, HitEndTimes = new int[] { 10960 }, FileName = "MCV0163.ani" } },
        { (8, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2080 }, HitEndTimes = new int[] { 3170 }, FileName = "MCV0166.ani" } },
        { (8, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3230 }, HitEndTimes = new int[] { 3640 }, FileName = "MCV0201.ani" } },
        { (8, 202, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1290, 2790 }, HitEndTimes = new int[] { 1650, 3140 }, FileName = "MCV0202.ani" } },
        { (8, 203, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1320, 2750, 5630 }, HitEndTimes = new int[] { 1740, 3110, 6050 }, FileName = "MCV0203.ani" } },
        { (8, 204, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 460, 2580, 3510, 5520 }, HitEndTimes = new int[] { 830, 2940, 3920, 5940 }, FileName = "MCV0204.ani" } },
        { (8, 205, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1910, 2940, 5320 }, HitEndTimes = new int[] { 2320, 3300, 5730 }, FileName = "MCV0205.ani" } },
        { (8, 206, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5806 }, HitEndTimes = new int[] { 6081 }, FileName = "MCV0206.ani" } },
        { (8, 207, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5806 }, HitEndTimes = new int[] { 6081 }, FileName = "MCV0207.ani" } },
        { (8, 208, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5806 }, HitEndTimes = new int[] { 6081 }, FileName = "MCV0208.ani" } },
        { (8, 209, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4327, 4764, 5164, 5666, 6168 }, HitEndTimes = new int[] { 4571, 4990, 5421, 5923, 6342 }, FileName = "MCV0209.ani" } },
        { (8, 210, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3200 }, HitEndTimes = new int[] { 3680 }, FileName = "MCV0210.ani" } },
        { (8, 211, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4400 }, HitEndTimes = new int[] { 4830 }, FileName = "MCV0211.ani" } },
        { (8, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2370, 4970 }, HitEndTimes = new int[] { 2770, 5380 }, FileName = "MCV0212.ani" } },
        { (8, 213, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 3060, 4340, 5700, 6980 }, HitEndTimes = new int[] { 2070, 3420, 4700, 6130, 7340 }, FileName = "MCV0213.ani" } },
        { (8, 214, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3840 }, HitEndTimes = new int[] { 5060 }, FileName = "MCV0214.ani" } },
        { (8, 215, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3840, 5180, 6720 }, HitEndTimes = new int[] { 4610, 5950, 8060 }, FileName = "MCV0215.ani" } },
        { (8, 216, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6410 }, HitEndTimes = new int[] { 7180 }, FileName = "MCV0216.ani" } },
        { (8, 217, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5810 }, HitEndTimes = new int[] { 6230 }, FileName = "MCV0217.ani" } },
        { (8, 218, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5450, 6190, 7560 }, HitEndTimes = new int[] { 5950, 6750, 8240 }, FileName = "MCV0218.ani" } },
        { (8, 219, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2580 }, HitEndTimes = new int[] { 3280 }, FileName = "MCV0219.ani" } },
        { (8, 222, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3200 }, HitEndTimes = new int[] { 3680 }, FileName = "MCV0222.ani" } },
        { (8, 223, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4400 }, HitEndTimes = new int[] { 4830 }, FileName = "MCV0223.ani" } },
        { (8, 224, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2370, 4970 }, HitEndTimes = new int[] { 2770, 5380 }, FileName = "MCV0224.ani" } },
        { (8, 225, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1260, 2200, 3100, 4080, 5100 }, HitEndTimes = new int[] { 1330, 2310, 3220, 4160, 5180 }, FileName = "MCV0225.ani" } },
        { (8, 226, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 300, 5030 }, HitEndTimes = new int[] { 600, 5210 }, FileName = "MCV0226.ani" } },
        { (8, 229, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8300 }, HitEndTimes = new int[] { 8630 }, FileName = "MCV0229.ani" } },
        { (8, 300, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1165, 2084, 2956, 3883, 4754 }, HitEndTimes = new int[] { 1410, 2330, 3201, 4176, 5000 }, FileName = "MCV0300.ani" } },
        { (8, 301, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1165, 2084, 2956, 3883, 4754 }, HitEndTimes = new int[] { 1410, 2330, 3201, 4176, 5000 }, FileName = "MCV0301.ani" } },
        { (8, 302, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2420, 4210, 6260 }, HitEndTimes = new int[] { 2850, 4650, 6630 }, FileName = "MCV0302.ani" } },
        { (8, 303, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5950 }, HitEndTimes = new int[] { 6340 }, FileName = "MCV0303.ani" } },
        { (8, 304, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4340 }, HitEndTimes = new int[] { 4850 }, FileName = "MCV0304.ani" } },
        { (8, 305, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3170, 4310, 5270 }, HitEndTimes = new int[] { 3530, 4730, 5630 }, FileName = "MCV0305.ani" } },
        { (8, 306, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5500 }, HitEndTimes = new int[] { 5900 }, FileName = "MCV0306.ani" } },
        { (8, 308, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4045, 4465, 4898, 5290, 5724 }, HitEndTimes = new int[] { 4272, 4658, 5092, 5483, 5879 }, FileName = "MCV0308.ani" } },
        { (8, 309, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4045, 4465, 4898, 5290, 5724 }, HitEndTimes = new int[] { 4272, 4658, 5092, 5483, 5879 }, FileName = "MCV0309.ani" } },
        { (8, 310, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4045, 4465, 4898, 5290, 5724 }, HitEndTimes = new int[] { 4272, 4658, 5092, 5483, 5879 }, FileName = "MCV0310.ani" } },
        { (8, 311, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4850 }, HitEndTimes = new int[] { 5095 }, FileName = "MCV0311.ani" } },
        { (8, 312, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4850 }, HitEndTimes = new int[] { 5095 }, FileName = "MCV0312.ani" } },
        { (8, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6720 }, HitEndTimes = new int[] { 7040 }, FileName = "MCV0314.ani" } },
        { (8, 315, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1410, 2138, 4806 }, HitEndTimes = new int[] { 1653, 2426, 5094 }, FileName = "MCV0315.ani" } },
        { (8, 316, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4976, 5551, 6223 }, HitEndTimes = new int[] { 5235, 5810, 6483 }, FileName = "MCV0316.ani" } },
        { (8, 317, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4442, 4897, 5481 }, HitEndTimes = new int[] { 4655, 5155, 5723 }, FileName = "MCV0317.ani" } },
        { (8, 318, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5806 }, HitEndTimes = new int[] { 6081 }, FileName = "MCV0318.ani" } },
        { (8, 319, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1165, 2084, 2956, 3883, 4754 }, HitEndTimes = new int[] { 1410, 2330, 3201, 4176, 5000 }, FileName = "MCV0319.ani" } },
        { (8, 320, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4327, 4764, 5164, 5666, 6168 }, HitEndTimes = new int[] { 4571, 4990, 5421, 5923, 6342 }, FileName = "MCV0320.ani" } },
        { (8, 326, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4512 }, HitEndTimes = new int[] { 4850 }, FileName = "MCV0326.ani" } },
        { (8, 326, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7200 }, HitEndTimes = new int[] { 7740 }, FileName = "MCV0326_1.ani" } },
        { (8, 327, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4045, 4465, 4898, 5290, 5724 }, HitEndTimes = new int[] { 4272, 4658, 5092, 5483, 5879 }, FileName = "MCV0327.ani" } },
        { (8, 327, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 7746, 8550, 9380, 10130, 10960 }, HitEndTimes = new int[] { 8180, 8920, 9750, 10500, 11240 }, FileName = "MCV0327_1.ani" } },
        { (8, 328, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4850 }, HitEndTimes = new int[] { 5095 }, FileName = "MCV0328.ani" } },
        { (8, 328, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7740 }, HitEndTimes = new int[] { 8130 }, FileName = "MCV0328_1.ani" } },
        { (8, 330, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5090 }, HitEndTimes = new int[] { 6150 }, FileName = "MCV0330.ani" } },
        { (8, 331, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3720 }, HitEndTimes = new int[] { 5230 }, FileName = "MCV0331.ani" } },
        { (8, 332, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2820 }, HitEndTimes = new int[] { 4000 }, FileName = "MCV0332.ani" } },
        { (8, 341, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5200 }, HitEndTimes = new int[] { 5350 }, FileName = "MCV0341.ani" } },
        { (8, 342, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3400, 4850 }, HitEndTimes = new int[] { 3550, 5000 }, FileName = "MCV0342.ani" } },
        { (8, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3200 }, HitEndTimes = new int[] { 3440 }, FileName = "MCV0401.ani" } },
        { (8, 402, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 830, 3470 }, HitEndTimes = new int[] { 920, 4200 }, FileName = "MCV0402.ani" } },
        { (8, 403, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1120, 3470 }, HitEndTimes = new int[] { 1260, 4000 }, FileName = "MCV0403.ani" } },
        { (8, 504, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3070 }, HitEndTimes = new int[] { 3300 }, FileName = "MCV0504.ani" } },
        { (8, 505, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1720, 2640 }, HitEndTimes = new int[] { 1920, 2840 }, FileName = "MCV0505.ani" } },
        { (8, 506, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3980 }, HitEndTimes = new int[] { 4240 }, FileName = "MCV0506.ani" } },
        { (8, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCV0900.ani" } },
        { (10, 103, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCZ0103.ani" } },
        { (10, 104, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 190, 2090 }, HitEndTimes = new int[] { 200, 2100 }, FileName = "MCZ0104.ani" } },
        { (10, 105, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 210, 2070 }, HitEndTimes = new int[] { 220, 2100 }, FileName = "MCZ0105.ani" } },
        { (10, 112, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2390 }, HitEndTimes = new int[] { 2950 }, FileName = "MCZ0112.ani" } },
        { (10, 113, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1090 }, HitEndTimes = new int[] { 1590 }, FileName = "MCZ0113.ani" } },
        { (10, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 210, 2070 }, HitEndTimes = new int[] { 220, 2100 }, FileName = "MCZ0121.ani" } },
        { (10, 121, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 1960 }, HitEndTimes = new int[] { 81, 1961 }, FileName = "MCZ0121.ani2" } },
        { (10, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCZ0124.ani" } },
        { (10, 124, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 170, 2540 }, HitEndTimes = new int[] { 180, 2600 }, FileName = "MCZ0124.ani2" } },
        { (10, 125, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2070 }, HitEndTimes = new int[] { 81, 2071 }, FileName = "MCZ0125.ani" } },
        { (10, 126, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2230 }, HitEndTimes = new int[] { 2330 }, FileName = "MCZ0126.ani" } },
        { (10, 127, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2100 }, HitEndTimes = new int[] { 2200 }, FileName = "MCZ0127.ani" } },
        { (10, 128, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2110, 4020, 5400 }, HitEndTimes = new int[] { 2210, 4120, 5500 }, FileName = "MCZ0128.ani" } },
        { (10, 129, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2110, 4020, 5400, 8700 }, HitEndTimes = new int[] { 2210, 4120, 5500, 8800 }, FileName = "MCZ0129.ani" } },
        { (10, 130, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2110, 4020, 5400, 7750 }, HitEndTimes = new int[] { 2210, 4120, 5500, 7850 }, FileName = "MCZ0130.ani" } },
        { (10, 131, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2110, 4020, 5800, 8060, 11920 }, HitEndTimes = new int[] { 2210, 4120, 5900, 8160, 12020 }, FileName = "MCZ0131.ani" } },
        { (10, 132, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2110, 4020, 7720 }, HitEndTimes = new int[] { 2210, 4120, 7820 }, FileName = "MCZ0132.ani" } },
        { (10, 133, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2110, 4020, 7800 }, HitEndTimes = new int[] { 2210, 4120, 7900 }, FileName = "MCZ0133.ani" } },
        { (10, 134, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3100 }, HitEndTimes = new int[] { 3200 }, FileName = "MCZ0134.ani" } },
        { (10, 135, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 190 }, HitEndTimes = new int[] { 200 }, FileName = "MCZ0135.ani" } },
        { (10, 135, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 190 }, HitEndTimes = new int[] { 200 }, FileName = "MCZ0135.ani2" } },
        { (10, 136, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2540 }, HitEndTimes = new int[] { 2640 }, FileName = "MCZ0136.ani" } },
        { (10, 138, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3890 }, HitEndTimes = new int[] { 3900 }, FileName = "MCZ0138.ani" } },
        { (10, 138, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3890 }, HitEndTimes = new int[] { 3900 }, FileName = "MCZ0138.ani2" } },
        { (10, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCZ0142.ani" } },
        { (10, 142, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2200 }, FileName = "MCZ0142.ani2" } },
        { (10, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2040 }, HitEndTimes = new int[] { 160, 2250 }, FileName = "MCZ0143.ani" } },
        { (10, 143, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 80, 2040 }, HitEndTimes = new int[] { 160, 2250 }, FileName = "MCZ0143.ani2" } },
        { (10, 145, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 240, 5880 }, HitEndTimes = new int[] { 430, 6000 }, FileName = "MCZ0145.ani2" } },
        { (10, 158, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 710, 1570, 2550, 3530, 4470 }, HitEndTimes = new int[] { 750, 1600, 2600, 3600, 4550 }, FileName = "MCZ0158.ani" } },
        { (10, 166, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCZ0166.ani" } },
        { (10, 201, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1650, 2900, 4850 }, HitEndTimes = new int[] { 1700, 3000, 4950 }, FileName = "MCZ0201.ani" } },
        { (10, 203, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1790, 3820 }, HitEndTimes = new int[] { 1890, 3920 }, FileName = "MCZ0203.ani" } },
        { (10, 204, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2500 }, FileName = "MCZ0204.ani" } },
        { (10, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2100 }, HitEndTimes = new int[] { 3150 }, FileName = "MCZ0205.ani" } },
        { (10, 209, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1140, 2400, 3150, 3830, 6350 }, HitEndTimes = new int[] { 1240, 2500, 3250, 3930, 6450 }, FileName = "MCZ0209.ani" } },
        { (10, 210, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3670 }, HitEndTimes = new int[] { 3770 }, FileName = "MCZ0210.ani" } },
        { (10, 211, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 950, 2650, 4250, 7230 }, HitEndTimes = new int[] { 1050, 2750, 4350, 7330 }, FileName = "MCZ0211.ani" } },
        { (10, 213, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3670 }, HitEndTimes = new int[] { 3770 }, FileName = "MCZ0213.ani" } },
        { (10, 214, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 950, 2650, 4250, 7230 }, HitEndTimes = new int[] { 1050, 2750, 4350, 7330 }, FileName = "MCZ0214.ani" } },
        { (10, 215, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3840, 5180, 6720 }, HitEndTimes = new int[] { 4610, 5950, 8060 }, FileName = "MCZ0215.ani" } },
        { (10, 216, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6410 }, HitEndTimes = new int[] { 7180 }, FileName = "MCZ0216.ani" } },
        { (10, 217, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1860, 3900 }, HitEndTimes = new int[] { 1960, 4000 }, FileName = "MCZ0217.ani" } },
        { (10, 218, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1340, 3860 }, HitEndTimes = new int[] { 1440, 3960 }, FileName = "MCZ0218.ani" } },
        { (10, 219, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1240, 2560 }, HitEndTimes = new int[] { 1340, 2660 }, FileName = "MCZ0219.ani" } },
        { (10, 220, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1700, 3830 }, HitEndTimes = new int[] { 1800, 3930 }, FileName = "MCZ0220.ani" } },
        { (10, 221, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1670, 4350 }, HitEndTimes = new int[] { 1770, 4450 }, FileName = "MCZ0221.ani" } },
        { (10, 222, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2430, 6270 }, HitEndTimes = new int[] { 2530, 6370 }, FileName = "MCZ0222.ani" } },
        { (10, 223, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 3060 }, HitEndTimes = new int[] { 1760, 3160 }, FileName = "MCZ0223.ani" } },
        { (10, 300, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1650, 2900, 4850 }, HitEndTimes = new int[] { 1700, 3000, 4950 }, FileName = "MCZ0300.ani" } },
        { (10, 301, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 3060, 4340, 5700, 6980 }, HitEndTimes = new int[] { 2070, 3420, 4700, 6130, 7340 }, FileName = "MCZ0301.ani" } },
        { (10, 302, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2420, 4210, 6260 }, HitEndTimes = new int[] { 2850, 4650, 6630 }, FileName = "MCZ0302.ani" } },
        { (10, 303, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5950 }, HitEndTimes = new int[] { 6340 }, FileName = "MCZ0303.ani" } },
        { (10, 304, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4340 }, HitEndTimes = new int[] { 4850 }, FileName = "MCZ0304.ani" } },
        { (10, 305, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3170, 4310, 5270 }, HitEndTimes = new int[] { 3530, 4730, 5630 }, FileName = "MCZ0305.ani" } },
        { (10, 306, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5500 }, HitEndTimes = new int[] { 5900 }, FileName = "MCZ0306.ani" } },
        { (10, 308, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 7746, 8550, 9380, 10130, 10960 }, HitEndTimes = new int[] { 8180, 8920, 9750, 10500, 11240 }, FileName = "MCZ0308.ani" } },
        { (10, 309, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 7746, 8550, 9380, 10130, 10960 }, HitEndTimes = new int[] { 8180, 8920, 9750, 10500, 11240 }, FileName = "MCZ0309.ani" } },
        { (10, 310, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 7746, 8550, 9380, 10130, 10960 }, HitEndTimes = new int[] { 8180, 8920, 9750, 10500, 11240 }, FileName = "MCZ0310.ani" } },
        { (10, 311, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7740 }, HitEndTimes = new int[] { 8130 }, FileName = "MCZ0311.ani" } },
        { (10, 312, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7740 }, HitEndTimes = new int[] { 8130 }, FileName = "MCZ0312.ani" } },
        { (10, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3700 }, HitEndTimes = new int[] { 3800 }, FileName = "MCZ0314.ani" } },
        { (10, 315, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 2820, 6340 }, HitEndTimes = new int[] { 2180, 3200, 6720 }, FileName = "MCZ0315.ani" } },
        { (10, 316, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 6140, 6850, 7680 }, HitEndTimes = new int[] { 6460, 7170, 8000 }, FileName = "MCZ0316.ani" } },
        { (10, 317, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5860, 6460, 7230 }, HitEndTimes = new int[] { 6140, 6800, 7550 }, FileName = "MCZ0317.ani" } },
        { (10, 318, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8030 }, HitEndTimes = new int[] { 8410 }, FileName = "MCZ0318.ani" } },
        { (10, 319, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 3060, 4340, 5700, 6980 }, HitEndTimes = new int[] { 2070, 3420, 4700, 6130, 7340 }, FileName = "MCZ0319.ani" } },
        { (10, 320, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 6720, 7400, 8020, 8800, 9580 }, HitEndTimes = new int[] { 7100, 7750, 8420, 9200, 9850 }, FileName = "MCZ0320.ani" } },
        { (10, 326, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7200 }, HitEndTimes = new int[] { 7740 }, FileName = "MCZ0326.ani" } },
        { (10, 327, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 7746, 8550, 9380, 10130, 10960 }, HitEndTimes = new int[] { 8180, 8920, 9750, 10500, 11240 }, FileName = "MCZ0327.ani" } },
        { (10, 328, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7740 }, HitEndTimes = new int[] { 8130 }, FileName = "MCZ0328.ani" } },
        { (10, 350, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1860, 3900 }, HitEndTimes = new int[] { 1960, 4000 }, FileName = "MCZ0350.ani" } },
        { (10, 351, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1340, 3860 }, HitEndTimes = new int[] { 1440, 3960 }, FileName = "MCZ0351.ani" } },
        { (10, 352, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1700, 3830 }, HitEndTimes = new int[] { 1800, 3930 }, FileName = "MCZ0352.ani" } },
        { (10, 353, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2500 }, FileName = "MCZ0353.ani" } },
        { (10, 354, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1670, 4350 }, HitEndTimes = new int[] { 1770, 4450 }, FileName = "MCZ0354.ani" } },
        { (10, 355, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2430, 6270 }, HitEndTimes = new int[] { 2530, 6370 }, FileName = "MCZ0355.ani" } },
        { (10, 356, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 3060 }, HitEndTimes = new int[] { 1760, 3160 }, FileName = "MCZ0356.ani" } },
        { (10, 401, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3200 }, HitEndTimes = new int[] { 3440 }, FileName = "MCZ0401.ani" } },
        { (10, 402, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 830, 3470 }, HitEndTimes = new int[] { 920, 4200 }, FileName = "MCZ0402.ani" } },
        { (10, 403, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1120, 3470 }, HitEndTimes = new int[] { 1260, 4000 }, FileName = "MCZ0403.ani" } },
        { (10, 501, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1860, 3900 }, HitEndTimes = new int[] { 1960, 4000 }, FileName = "MCZ0501.ani" } },
        { (10, 502, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1340, 3860 }, HitEndTimes = new int[] { 1440, 3960 }, FileName = "MCZ0502.ani" } },
        { (10, 503, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1700, 3830 }, HitEndTimes = new int[] { 1800, 3930 }, FileName = "MCZ0503.ani" } },
        { (10, 504, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 810, 2090, 4240 }, HitEndTimes = new int[] { 860, 2140, 4340 }, FileName = "MCZ0504.ani" } },
        { (10, 505, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1790, 3820 }, HitEndTimes = new int[] { 1890, 3920 }, FileName = "MCZ0505.ani" } },
        { (10, 506, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1670, 4350 }, HitEndTimes = new int[] { 1770, 4450 }, FileName = "MCZ0506.ani" } },
        { (10, 507, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2430, 6270 }, HitEndTimes = new int[] { 2530, 6370 }, FileName = "MCZ0507.ani" } },
        { (10, 508, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 3060 }, HitEndTimes = new int[] { 1760, 3160 }, FileName = "MCZ0508.ani" } },
        { (10, 510, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5670 }, HitEndTimes = new int[] { 5770 }, FileName = "MCZ0510.ani" } },
        { (10, 511, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 1700, 3070, 4770, 7020 }, HitEndTimes = new int[] { 1800, 3170, 4870, 7120 }, FileName = "MCZ0511.ani" } },
        { (10, 900, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2280 }, HitEndTimes = new int[] { 2940 }, FileName = "MCZ0900.ani2" } },
        { (1, 203, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2250, 6130 }, HitEndTimes = new int[] { 3060, 6880 }, FileName = "MCK0203.ani" } },
        { (1, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 190, 6590 }, HitEndTimes = new int[] { 450, 7490 }, FileName = "MCK0210.ani" } },
        { (1, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1450, 7490 }, HitEndTimes = new int[] { 1800, 8190 }, FileName = "MCK0212.ani" } },
        { (1, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6720 }, HitEndTimes = new int[] { 7040 }, FileName = "MCK0314.ani" } },
        { (1, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCK0150.ani" } },
        { (1, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCK0150.ani" } },
        { (1, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCK0150.ani" } },
        { (1, 207, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6950 }, HitEndTimes = new int[] { 7600 }, FileName = "AMCK0207.ani" } },
        { (1, 208, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2810, 5270 }, HitEndTimes = new int[] { 3170, 5570 }, FileName = "AMCK0208.ani" } },
        { (1, 300, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1910, 4340, 6240 }, HitEndTimes = new int[] { 2230, 4660, 6640 }, FileName = "AMCK0300.ani" } },
        { (1, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5750 }, HitEndTimes = new int[] { 6470 }, FileName = "AMCK0301.ani" } },
        { (1, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8060 }, HitEndTimes = new int[] { 9540 }, FileName = "AMCK0303.ani" } },
        { (1, 304, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3970 }, HitEndTimes = new int[] { 7300 }, FileName = "AMCK0304.ani" } },
        { (1, 305, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4030, 5310, 7100 }, HitEndTimes = new int[] { 5120, 6850, 9280 }, FileName = "AMCK0305.ani" } },
        { (1, 307, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7550 }, HitEndTimes = new int[] { 9220 }, FileName = "AMCK0307.ani" } },
        { (1, 308, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4460 }, HitEndTimes = new int[] { 5400 }, FileName = "AMCK0308.ani" } },
        { (1, 309, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4270, 7820 }, HitEndTimes = new int[] { 4730, 8280 }, FileName = "AMCK0309.ani" } },
        { (1, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1340, 5890, 7360 }, HitEndTimes = new int[] { 4290, 6910, 9150 }, FileName = "AMCK0311.ani" } },
        { (1, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 8830 }, FileName = "AMCK0313.ani" } },
        { (1, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCK0150.ani" } },
        { (1, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCK0150.ani" } },
        { (1, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCK0150.ani" } },
        { (1, 206, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3450 }, HitEndTimes = new int[] { 4000 }, FileName = "BMCK0206.ani" } },
        { (1, 207, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6950 }, HitEndTimes = new int[] { 7180 }, FileName = "BMCK0207.ani" } },
        { (1, 209, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4910 }, HitEndTimes = new int[] { 5270 }, FileName = "BMCK0209.ani" } },
        { (1, 300, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7660 }, HitEndTimes = new int[] { 8720 }, FileName = "BMCK0300.ani" } },
        { (1, 302, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2430, 7300 }, HitEndTimes = new int[] { 4930, 9340 }, FileName = "BMCK0302.ani" } },
        { (1, 303, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1020, 4160, 7300 }, HitEndTimes = new int[] { 2880, 6020, 9090 }, FileName = "BMCK0303.ani" } },
        { (1, 304, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7040 }, HitEndTimes = new int[] { 9470 }, FileName = "BMCK0304.ani" } },
        { (1, 307, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8130 }, HitEndTimes = new int[] { 9220 }, FileName = "BMCK0307.ani" } },
        { (1, 308, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 390, 2100, 6040 }, HitEndTimes = new int[] { 850, 2890, 6700 }, FileName = "BMCK0308.ani" } },
        { (1, 309, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4120 }, HitEndTimes = new int[] { 4690 }, FileName = "BMCK0309.ani" } },
        { (1, 311, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1790, 3350, 6240 }, HitEndTimes = new int[] { 2770, 4860, 7920 }, FileName = "BMCK0311.ani" } },
        { (1, 312, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3460, 7230 }, HitEndTimes = new int[] { 5890, 8960 }, FileName = "BMCK0312.ani" } },
        { (1, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCK0150.ani" } },
        { (1, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCK0150.ani" } },
        { (1, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCK0150.ani" } },
        { (1, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1668 }, HitEndTimes = new int[] { 2009 }, FileName = "AMCK0315.ani" } },
        { (1, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3684 }, HitEndTimes = new int[] { 4852 }, FileName = "AMCK0317.ani" } },
        { (1, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3836 }, HitEndTimes = new int[] { 4852 }, FileName = "AMCK0318.ani" } },
        { (1, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4897 }, HitEndTimes = new int[] { 5049 }, FileName = "AMCK0319.ani" } },
        { (1, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4708 }, HitEndTimes = new int[] { 4996 }, FileName = "AMCK0320.ani" } },
        { (1, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5243 }, HitEndTimes = new int[] { 6110 }, FileName = "AMCK0327.ani" } },
        { (1, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4900 }, HitEndTimes = new int[] { 4990 }, FileName = "AMCK0330.ani" } },
        { (1, 338, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4490 }, HitEndTimes = new int[] { 6000 }, FileName = "AMCK0338.ani" } },
        { (1, 339, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5000, 5500, 6000 }, HitEndTimes = new int[] { 5500, 6000, 6500 }, FileName = "AMCK0339.ani" } },
        { (1, 342, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4730, 6460 }, HitEndTimes = new int[] { 4880, 6610 }, FileName = "AMCK0342.ani" } },
        { (1, 315, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3442 }, HitEndTimes = new int[] { 3616 }, FileName = "BMCK0315.ani" } },
        { (1, 316, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2585, 5610 }, HitEndTimes = new int[] { 2873, 5640 }, FileName = "BMCK0316.ani" } },
        { (1, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3684 }, HitEndTimes = new int[] { 5193 }, FileName = "BMCK0317.ani" } },
        { (1, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3540 }, HitEndTimes = new int[] { 4222 }, FileName = "BMCK0318.ani" } },
        { (1, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4806 }, HitEndTimes = new int[] { 5238 }, FileName = "BMCK0319.ani" } },
        { (1, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3836 }, HitEndTimes = new int[] { 4222 }, FileName = "BMCK0326.ani" } },
        { (1, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5243 }, HitEndTimes = new int[] { 6110 }, FileName = "BMCK0327.ani" } },
        { (1, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4900 }, HitEndTimes = new int[] { 4990 }, FileName = "BMCK0330.ani" } },
        { (1, 338, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4490 }, HitEndTimes = new int[] { 6000 }, FileName = "BMCK0338.ani" } },
        { (1, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5380 }, HitEndTimes = new int[] { 5530 }, FileName = "BMCK0341.ani" } },
        { (2, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8240 }, HitEndTimes = new int[] { 8710 }, FileName = "MCS0201.ani" } },
        { (2, 203, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7090 }, HitEndTimes = new int[] { 8350 }, FileName = "MCS0203.ani" } },
        { (2, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5900 }, HitEndTimes = new int[] { 7050 }, FileName = "MCS0202.ani" } },
        { (2, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 8900 }, HitEndTimes = new int[] { 1410, 9660 }, FileName = "MCS0210.ani" } },
        { (2, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1110, 8000 }, HitEndTimes = new int[] { 1280, 8830 }, FileName = "MCS0211.ani" } },
        { (2, 314, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3650, 4800, 6530 }, HitEndTimes = new int[] { 3900, 4990, 7040 }, FileName = "MCS0314.ani" } },
        { (2, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2710 }, HitEndTimes = new int[] { 3530 }, FileName = "MCS0205.ani" } },
        { (2, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCS0150.ani" } },
        { (2, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCS0150.ani" } },
        { (2, 206, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6350 }, HitEndTimes = new int[] { 7420 }, FileName = "AMCS0206.ani" } },
        { (2, 207, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5750 }, HitEndTimes = new int[] { 6710 }, FileName = "AMCS0207.ani" } },
        { (2, 208, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6710 }, HitEndTimes = new int[] { 7660 }, FileName = "AMCS0208.ani" } },
        { (2, 300, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8060 }, HitEndTimes = new int[] { 8790 }, FileName = "AMCS0300.ani" } },
        { (2, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6470 }, HitEndTimes = new int[] { 7120 }, FileName = "AMCS0301.ani" } },
        { (2, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7300 }, HitEndTimes = new int[] { 8770 }, FileName = "AMCS0303.ani" } },
        { (2, 304, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7300 }, HitEndTimes = new int[] { 9150 }, FileName = "AMCS0304.ani" } },
        { (2, 305, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2240, 5310, 7620 }, HitEndTimes = new int[] { 3970, 7040, 9150 }, FileName = "AMCS0305.ani" } },
        { (2, 307, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2510, 4670, 6770 }, HitEndTimes = new int[] { 4070, 6170, 8200 }, FileName = "AMCS0307.ani" } },
        { (2, 308, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1820, 3860 }, HitEndTimes = new int[] { 2420, 4300 }, FileName = "AMCS0308.ani" } },
        { (2, 310, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5450 }, HitEndTimes = new int[] { 6040 }, FileName = "AMCS0310.ani" } },
        { (2, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1540, 4420, 7870 }, HitEndTimes = new int[] { 3460, 6340, 9280 }, FileName = "AMCS0311.ani" } },
        { (2, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7170 }, HitEndTimes = new int[] { 8770 }, FileName = "AMCS0313.ani" } },
        { (2, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCS0150.ani" } },
        { (2, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCS0150.ani" } },
        { (2, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCS0150.ani" } },
        { (2, 207, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5810 }, HitEndTimes = new int[] { 6230 }, FileName = "BMCS0207.ani" } },
        { (2, 208, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1600, 4100, 7040 }, HitEndTimes = new int[] { 1980, 4540, 7490 }, FileName = "BMCS0208.ani" } },
        { (2, 300, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6110 }, HitEndTimes = new int[] { 6650 }, FileName = "BMCS0300.ani" } },
        { (2, 302, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2750, 5250, 7040 }, HitEndTimes = new int[] { 4420, 6660, 9410 }, FileName = "BMCS0302.ani" } },
        { (2, 303, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7870 }, HitEndTimes = new int[] { 9410 }, FileName = "BMCS0303.ani" } },
        { (2, 305, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1740, 6350 }, HitEndTimes = new int[] { 4250, 8380 }, FileName = "BMCS0305.ani" } },
        { (2, 306, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2510, 6170 }, HitEndTimes = new int[] { 3410, 8380 }, FileName = "BMCS0306.ani" } },
        { (2, 308, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1700, 4880, 6850 }, HitEndTimes = new int[] { 2100, 5900, 7600 }, FileName = "BMCS0308.ani" } },
        { (2, 310, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2580, 6240 }, HitEndTimes = new int[] { 3320, 6980 }, FileName = "BMCS0310.ani" } },
        { (2, 311, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1660, 4540, 6660 }, HitEndTimes = new int[] { 3710, 5760, 8640 }, FileName = "BMCS0311.ani" } },
        { (2, 312, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2500, 6910 }, HitEndTimes = new int[] { 5890, 8960 }, FileName = "BMCS0312.ani" } },
        { (2, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCS0150.ani" } },
        { (2, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCS0150.ani" } },
        { (2, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCS0150.ani" } },
        { (2, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5049 }, HitEndTimes = new int[] { 5572 }, FileName = "AMCS0315.ani" } },
        { (2, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4510 }, HitEndTimes = new int[] { 5776 }, FileName = "AMCS0317.ani" } },
        { (2, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3154 }, HitEndTimes = new int[] { 4806 }, FileName = "AMCS0318.ani" } },
        { (2, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4609 }, HitEndTimes = new int[] { 5049 }, FileName = "AMCS0319.ani" } },
        { (2, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3442 }, HitEndTimes = new int[] { 3639 }, FileName = "AMCS0320.ani" } },
        { (2, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3688 }, HitEndTimes = new int[] { 5143 }, FileName = "AMCS0327.ani" } },
        { (2, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3010 }, HitEndTimes = new int[] { 5090 }, FileName = "AMCS0330.ani" } },
        { (2, 338, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5080 }, HitEndTimes = new int[] { 6840 }, FileName = "AMCS0338.ani" } },
        { (2, 339, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2930, 4720 }, HitEndTimes = new int[] { 3550, 6730 }, FileName = "AMCS0339.ani" } },
        { (2, 342, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 6240, 6390 }, HitEndTimes = new int[] { 6390, 6540 }, FileName = "AMCS0342.ani" } },
        { (2, 315, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3730 }, HitEndTimes = new int[] { 4935 }, FileName = "BMCS0315.ani" } },
        { (2, 316, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1781, 5511 }, HitEndTimes = new int[] { 2070, 5913 }, FileName = "BMCS0316.ani" } },
        { (2, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3442 }, HitEndTimes = new int[] { 4996 }, FileName = "BMCS0317.ani" } },
        { (2, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3593 }, HitEndTimes = new int[] { 4655 }, FileName = "BMCS0318.ani" } },
        { (2, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4996 }, HitEndTimes = new int[] { 5579 }, FileName = "BMCS0319.ani" } },
        { (2, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3688 }, HitEndTimes = new int[] { 5143 }, FileName = "BMCS0327.ani" } },
        { (2, 331, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2630, 3760, 5160 }, HitEndTimes = new int[] { 3060, 4090, 5790 }, FileName = "BMCS0331.ani" } },
        { (2, 338, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5080 }, HitEndTimes = new int[] { 6840 }, FileName = "BMCS0338.ani" } },
        { (2, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5810 }, HitEndTimes = new int[] { 5960 }, FileName = "BMCS0341.ani" } },
        { (2, 342, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 6050, 6200 }, HitEndTimes = new int[] { 6200, 6350 }, FileName = "BMCS0342.ani" } },
        { (3, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8550 }, HitEndTimes = new int[] { 10030 }, FileName = "MCB0201.ani" } },
        { (3, 203, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 6240, 7510, 9440 }, HitEndTimes = new int[] { 6760, 8030, 10260 }, FileName = "MCB0203.ani" } },
        { (3, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1150, 7550 }, HitEndTimes = new int[] { 1280, 8130 }, FileName = "MCB0210.ani" } },
        { (3, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 7420 }, HitEndTimes = new int[] { 1340, 7870 }, FileName = "MCB0212.ani" } },
        { (3, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 6980 }, FileName = "MCB0314.ani" } },
        { (3, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2080 }, HitEndTimes = new int[] { 2610 }, FileName = "MCB0205.ani" } },
        { (3, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCB0150.ani" } },
        { (3, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCB0150.ani" } },
        { (3, 206, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1680, 5810 }, HitEndTimes = new int[] { 2390, 7180 }, FileName = "AMCB0206.ani" } },
        { (3, 207, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6050 }, HitEndTimes = new int[] { 6950 }, FileName = "AMCB0207.ani" } },
        { (3, 209, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2230, 6040 }, HitEndTimes = new int[] { 2690, 6310 }, FileName = "AMCB0209.ani" } },
        { (3, 300, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2510, 3770, 7070 }, HitEndTimes = new int[] { 3240, 4690, 7600 }, FileName = "AMCB0300.ani" } },
        { (3, 302, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1500, 2870, 7000 }, HitEndTimes = new int[] { 2690, 4010, 8800 }, FileName = "AMCB0302.ani" } },
        { (3, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6850 }, HitEndTimes = new int[] { 9340 }, FileName = "AMCB0303.ani" } },
        { (3, 305, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 7300 }, HitEndTimes = new int[] { 3710, 8900 }, FileName = "AMCB0305.ani" } },
        { (3, 306, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3010, 6660 }, HitEndTimes = new int[] { 4740, 8510 }, FileName = "AMCB0306.ani" } },
        { (3, 308, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 750, 2920, 5220 }, HitEndTimes = new int[] { 1090, 3390, 5630 }, FileName = "AMCB0308.ani" } },
        { (3, 310, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1640, 4140, 6900 }, HitEndTimes = new int[] { 2230, 4800, 7290 }, FileName = "AMCB0310.ani" } },
        { (3, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1920, 4860, 7940 }, HitEndTimes = new int[] { 4220, 6980, 9340 }, FileName = "AMCB0311.ani" } },
        { (3, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5500 }, HitEndTimes = new int[] { 8700 }, FileName = "AMCB0313.ani" } },
        { (3, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCB0150.ani" } },
        { (3, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCB0150.ani" } },
        { (3, 206, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6950 }, FileName = "BMCB0206.ani" } },
        { (3, 208, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5750 }, HitEndTimes = new int[] { 6530 }, FileName = "BMCB0208.ani" } },
        { (3, 209, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3470, 5690 }, HitEndTimes = new int[] { 4430, 6170 }, FileName = "BMCB0209.ani" } },
        { (3, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6590 }, HitEndTimes = new int[] { 7240 }, FileName = "BMCB0301.ani" } },
        { (3, 302, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1860, 4350, 6980 }, HitEndTimes = new int[] { 3460, 6270, 9470 }, FileName = "BMCB0302.ani" } },
        { (3, 304, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5210, 6170 }, HitEndTimes = new int[] { 5990, 8560 }, FileName = "BMCB0304.ani" } },
        { (3, 305, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3460, 5630, 6780 }, HitEndTimes = new int[] { 4930, 6460, 8960 }, FileName = "BMCB0305.ani" } },
        { (3, 307, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3010, 6020, 8900 }, HitEndTimes = new int[] { 4540, 7360, 9540 }, FileName = "BMCB0307.ani" } },
        { (3, 308, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3190, 5490 }, HitEndTimes = new int[] { 3730, 6040 }, FileName = "BMCB0308.ani" } },
        { (3, 310, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1700, 6920 }, HitEndTimes = new int[] { 2310, 7660 }, FileName = "BMCB0310.ani" } },
        { (3, 311, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1600, 4420, 7100 }, HitEndTimes = new int[] { 3460, 5890, 8830 }, FileName = "BMCB0311.ani" } },
        { (3, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 8770 }, FileName = "BMCB0313.ani" } },
        { (3, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCB0150.ani" } },
        { (3, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCB0150.ani" } },
        { (3, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCB0150.ani" } },
        { (3, 316, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2585, 3502 }, HitEndTimes = new int[] { 2926, 4420 }, FileName = "AMCB0316.ani" } },
        { (3, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4366 }, HitEndTimes = new int[] { 5238 }, FileName = "AMCB0317.ani" } },
        { (3, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3593 }, HitEndTimes = new int[] { 4708 }, FileName = "AMCB0318.ani" } },
        { (3, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4753 }, HitEndTimes = new int[] { 4996 }, FileName = "AMCB0320.ani" } },
        { (3, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5092 }, HitEndTimes = new int[] { 6063 }, FileName = "AMCB0326.ani" } },
        { (3, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4126 }, HitEndTimes = new int[] { 5433 }, FileName = "AMCB0327.ani" } },
        { (3, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4860 }, HitEndTimes = new int[] { 6160 }, FileName = "AMCB0330.ani" } },
        { (3, 338, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2600, 4500 }, HitEndTimes = new int[] { 4500, 6400 }, FileName = "AMCB0338.ani" } },
        { (3, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4500 }, HitEndTimes = new int[] { 4650 }, FileName = "AMCB0341.ani" } },
        { (3, 342, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1890, 5330 }, HitEndTimes = new int[] { 2040, 5480 }, FileName = "AMCB0342.ani" } },
        { (3, 316, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2411, 5852 }, HitEndTimes = new int[] { 2638, 6201 }, FileName = "BMCB0316.ani" } },
        { (3, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2229 }, HitEndTimes = new int[] { 3836 }, FileName = "BMCB0318.ani" } },
        { (3, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4708 }, HitEndTimes = new int[] { 5094 }, FileName = "BMCB0319.ani" } },
        { (3, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4996 }, HitEndTimes = new int[] { 5382 }, FileName = "BMCB0320.ani" } },
        { (3, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5092 }, HitEndTimes = new int[] { 6063 }, FileName = "BMCB0326.ani" } },
        { (3, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4860 }, HitEndTimes = new int[] { 6160 }, FileName = "BMCB0330.ani" } },
        { (3, 331, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5530 }, HitEndTimes = new int[] { 6400 }, FileName = "BMCB0331.ani" } },
        { (3, 339, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4800, 5340, 5880 }, HitEndTimes = new int[] { 5340, 5880, 6420 }, FileName = "BMCB0339.ani" } },
        { (3, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5050 }, HitEndTimes = new int[] { 5200 }, FileName = "BMCB0341.ani" } },
        { (4, 317, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1530 }, HitEndTimes = new int[] { 5340 }, FileName = "MCA0317.ani" } },
        { (4, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3880 }, HitEndTimes = new int[] { 6040 }, FileName = "MCA0202.ani" } },
        { (4, 203, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1450 }, HitEndTimes = new int[] { 4660 }, FileName = "MCA0203.ani" } },
        { (4, 204, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4400 }, HitEndTimes = new int[] { 6640 }, FileName = "MCA0204.ani" } },
        { (4, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 830, 7550 }, HitEndTimes = new int[] { 1220, 8190 }, FileName = "MCA0210.ani" } },
        { (4, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 380, 8450 }, HitEndTimes = new int[] { 680, 9130 }, FileName = "MCA0211.ani" } },
        { (4, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7040 }, HitEndTimes = new int[] { 8190 }, FileName = "MCA0314.ani" } },
        { (4, 137, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCA0137.ani" } },
        { (4, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1540 }, HitEndTimes = new int[] { 2460 }, FileName = "MCA0205.ani" } },
        { (4, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1540 }, HitEndTimes = new int[] { 2460 }, FileName = "MCA0205.ani" } },
        { (4, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1540 }, HitEndTimes = new int[] { 2460 }, FileName = "MCA0205.ani" } },
        { (4, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1540 }, HitEndTimes = new int[] { 2460 }, FileName = "MCA0205.ani" } },
        { (4, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1540 }, HitEndTimes = new int[] { 2460 }, FileName = "MCA0205.ani" } },
        { (4, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCA0150.ani" } },
        { (4, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCA0150.ani" } },
        { (4, 206, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 790, 5520 }, HitEndTimes = new int[] { 1120, 6310 }, FileName = "AMCA0206.ani" } },
        { (4, 208, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2820 }, HitEndTimes = new int[] { 6240 }, FileName = "AMCA0208.ani" } },
        { (4, 209, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2760, 5120, 7030 }, HitEndTimes = new int[] { 3350, 5580, 7550 }, FileName = "AMCA0209.ani" } },
        { (4, 301, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 130, 2100, 6440 }, HitEndTimes = new int[] { 460, 2370, 7290 }, FileName = "AMCA0301.ani" } },
        { (4, 304, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5120 }, HitEndTimes = new int[] { 6110 }, FileName = "AMCA0304.ani" } },
        { (4, 305, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3150 }, HitEndTimes = new int[] { 7100 }, FileName = "AMCA0305.ani" } },
        { (4, 306, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 660, 5190 }, HitEndTimes = new int[] { 2230, 6770 }, FileName = "AMCA0306.ani" } },
        { (4, 308, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3550, 6370 }, HitEndTimes = new int[] { 4800, 7100 }, FileName = "AMCA0308.ani" } },
        { (4, 309, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1050, 3350, 6700 }, HitEndTimes = new int[] { 1710, 3940, 7290 }, FileName = "AMCA0309.ani" } },
        { (4, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 960, 4350, 6590 }, HitEndTimes = new int[] { 1920, 5380, 8190 }, FileName = "AMCA0311.ani" } },
        { (4, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2620 }, HitEndTimes = new int[] { 7100 }, FileName = "AMCA0313.ani" } },
        { (4, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCA0150.ani" } },
        { (4, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCA0150.ani" } },
        { (4, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCA0150.ani" } },
        { (4, 206, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 990 }, HitEndTimes = new int[] { 6110 }, FileName = "BMCA0206.ani" } },
        { (4, 208, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3020 }, HitEndTimes = new int[] { 5910 }, FileName = "BMCA0208.ani" } },
        { (4, 209, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2820 }, HitEndTimes = new int[] { 4860 }, FileName = "BMCA0209.ani" } },
        { (4, 300, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 590, 6310 }, HitEndTimes = new int[] { 2170, 7030 }, FileName = "BMCA0300.ani" } },
        { (4, 302, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4360 }, HitEndTimes = new int[] { 5590 }, FileName = "BMCA0302.ani" } },
        { (4, 303, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1510, 4470 }, HitEndTimes = new int[] { 2230, 5120 }, FileName = "BMCA0303.ani" } },
        { (4, 304, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5320 }, HitEndTimes = new int[] { 6770 }, FileName = "BMCA0304.ani" } },
        { (4, 306, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3280 }, HitEndTimes = new int[] { 6040 }, FileName = "BMCA0306.ani" } },
        { (4, 307, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1640, 3220, 6040 }, HitEndTimes = new int[] { 2230, 4200, 7030 }, FileName = "BMCA0307.ani" } },
        { (4, 309, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1250, 5720, 7620 }, HitEndTimes = new int[] { 1970, 6700, 8740 }, FileName = "BMCA0309.ani" } },
        { (4, 311, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3010, 6720 }, HitEndTimes = new int[] { 4030, 7620 }, FileName = "BMCA0311.ani" } },
        { (4, 312, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2880, 6910 }, HitEndTimes = new int[] { 4420, 7870 }, FileName = "BMCA0312.ani" } },
        { (4, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5380 }, HitEndTimes = new int[] { 7680 }, FileName = "BMCA0313.ani" } },
        { (4, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCA0150.ani" } },
        { (4, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCA0150.ani" } },
        { (4, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4533 }, HitEndTimes = new int[] { 5799 }, FileName = "AMCA0315.ani" } },
        { (4, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4078 }, HitEndTimes = new int[] { 4806 }, FileName = "AMCA0318.ani" } },
        { (4, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5721 }, HitEndTimes = new int[] { 6110 }, FileName = "AMCA0326.ani" } },
        { (4, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3150 }, HitEndTimes = new int[] { 5430 }, FileName = "AMCA0330.ani" } },
        { (4, 338, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2230 }, HitEndTimes = new int[] { 5870 }, FileName = "AMCA0338.ani" } },
        { (4, 315, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5223, 6254 }, HitEndTimes = new int[] { 5397, 6603 }, FileName = "BMCA0315.ani" } },
        { (4, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3010 }, HitEndTimes = new int[] { 3154 }, FileName = "BMCA0318.ani" } },
        { (4, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4897 }, HitEndTimes = new int[] { 5382 }, FileName = "BMCA0319.ani" } },
        { (4, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5721 }, HitEndTimes = new int[] { 6110 }, FileName = "BMCA0326.ani" } },
        { (4, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6020 }, HitEndTimes = new int[] { 6590 }, FileName = "BMCA0327.ani" } },
        { (4, 338, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2230 }, HitEndTimes = new int[] { 5870 }, FileName = "BMCA0338.ani" } },
        { (4, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4330 }, HitEndTimes = new int[] { 4510 }, FileName = "BMCA0341.ani" } },
        { (5, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6070 }, HitEndTimes = new int[] { 7400 }, FileName = "MCE0202.ani" } },
        { (5, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8580 }, HitEndTimes = new int[] { 1100, 9150 }, FileName = "MCE0211.ani" } },
        { (5, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8960 }, HitEndTimes = new int[] { 1020, 9340 }, FileName = "MCE0212.ani" } },
        { (5, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 7940 }, FileName = "MCE0314.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCE0150.ani" } },
        { (5, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCE0150.ani" } },
        { (5, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCE0150.ani" } },
        { (5, 206, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5480 }, HitEndTimes = new int[] { 10390 }, FileName = "AMCE0206.ani" } },
        { (5, 207, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 6110 }, FileName = "AMCE0207.ani" } },
        { (5, 209, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6410 }, HitEndTimes = new int[] { 7180 }, FileName = "AMCE0209.ani" } },
        { (5, 300, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5870 }, HitEndTimes = new int[] { 6770 }, FileName = "AMCE0300.ani" } },
        { (5, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6410 }, HitEndTimes = new int[] { 7180 }, FileName = "AMCE0301.ani" } },
        { (5, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 8960 }, FileName = "AMCE0303.ani" } },
        { (5, 304, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2040, 4070, 5690 }, HitEndTimes = new int[] { 3530, 5270, 8320 }, FileName = "AMCE0304.ani" } },
        { (5, 306, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7230 }, HitEndTimes = new int[] { 9150 }, FileName = "AMCE0306.ani" } },
        { (5, 307, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6270 }, HitEndTimes = new int[] { 8960 }, FileName = "AMCE0307.ani" } },
        { (5, 308, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2820, 4990, 7160 }, HitEndTimes = new int[] { 3150, 5520, 8010 }, FileName = "AMCE0308.ani" } },
        { (5, 310, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3480, 5520, 7360 }, HitEndTimes = new int[] { 4140, 6370, 8280 }, FileName = "AMCE0310.ani" } },
        { (5, 312, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4540, 6340 }, HitEndTimes = new int[] { 5820, 9090 }, FileName = "AMCE0312.ani" } },
        { (5, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6440 }, HitEndTimes = new int[] { 8870 }, FileName = "AMCE0313.ani" } },
        { (5, 206, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5730 }, HitEndTimes = new int[] { 6760 }, FileName = "BMCE0206.ani" } },
        { (5, 207, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4800 }, HitEndTimes = new int[] { 6090 }, FileName = "BMCE0207.ani" } },
        { (5, 209, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6650 }, HitEndTimes = new int[] { 7780 }, FileName = "BMCE0209.ani" } },
        { (5, 300, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5330 }, HitEndTimes = new int[] { 6350 }, FileName = "BMCE0300.ani" } },
        { (5, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4010 }, HitEndTimes = new int[] { 5690 }, FileName = "BMCE0301.ani" } },
        { (5, 305, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6910 }, HitEndTimes = new int[] { 9220 }, FileName = "BMCE0305.ani" } },
        { (5, 306, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 9090 }, FileName = "BMCE0306.ani" } },
        { (5, 307, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6460 }, HitEndTimes = new int[] { 9340 }, FileName = "BMCE0307.ani" } },
        { (5, 309, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1970, 3730, 7390 }, HitEndTimes = new int[] { 2370, 4140, 8270 }, FileName = "BMCE0309.ani" } },
        { (5, 310, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 950, 3190, 6650 }, HitEndTimes = new int[] { 1490, 3660, 7260 }, FileName = "BMCE0310.ani" } },
        { (5, 312, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3010, 6590 }, HitEndTimes = new int[] { 5500, 8960 }, FileName = "BMCE0312.ani" } },
        { (5, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5440 }, HitEndTimes = new int[] { 8640 }, FileName = "BMCE0313.ani" } },
        { (5, 315, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2699, 6087 }, HitEndTimes = new int[] { 3154, 6716 }, FileName = "AMCE0315.ani" } },
        { (5, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3108 }, HitEndTimes = new int[] { 4852 }, FileName = "AMCE0317.ani" } },
        { (5, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4173 }, HitEndTimes = new int[] { 4660 }, FileName = "AMCE0318.ani" } },
        { (5, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4950 }, HitEndTimes = new int[] { 5140 }, FileName = "AMCE0319.ani" } },
        { (5, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5966 }, HitEndTimes = new int[] { 6451 }, FileName = "AMCE0326.ani" } },
        { (5, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4514 }, HitEndTimes = new int[] { 5389 }, FileName = "AMCE0327.ani" } },
        { (5, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4120 }, HitEndTimes = new int[] { 5720 }, FileName = "AMCE0330.ani" } },
        { (5, 338, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2630 }, HitEndTimes = new int[] { 7120 }, FileName = "AMCE0338.ani" } },
        { (5, 339, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2220, 5000 }, HitEndTimes = new int[] { 3800, 5850 }, FileName = "AMCE0339.ani" } },
        { (5, 342, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4800, 4950, 5100 }, HitEndTimes = new int[] { 4950, 5100, 5250 }, FileName = "AMCE0342.ani" } },
        { (5, 315, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3154, 4821 }, HitEndTimes = new int[] { 3616, 5170 }, FileName = "BMCE0315.ani" } },
        { (5, 316, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5434 }, HitEndTimes = new int[] { 5869 }, FileName = "BMCE0316.ani" } },
        { (5, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2714 }, HitEndTimes = new int[] { 4806 }, FileName = "BMCE0317.ani" } },
        { (5, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5044 }, HitEndTimes = new int[] { 6116 }, FileName = "BMCE0318.ani" } },
        { (5, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4708 }, HitEndTimes = new int[] { 4950 }, FileName = "BMCE0320.ani" } },
        { (5, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5966 }, HitEndTimes = new int[] { 6451 }, FileName = "BMCE0326.ani" } },
        { (5, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4514 }, HitEndTimes = new int[] { 5389 }, FileName = "BMCE0327.ani" } },
        { (5, 331, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2090 }, HitEndTimes = new int[] { 6110 }, FileName = "BMCE0331.ani" } },
        { (5, 338, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2630 }, HitEndTimes = new int[] { 7120 }, FileName = "BMCE0338.ani" } },
        { (5, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5300 }, HitEndTimes = new int[] { 5450 }, FileName = "BMCE0341.ani" } },
        { (7, 301, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3300 }, HitEndTimes = new int[] { 3830 }, FileName = "MCM0301.ani" } },
        { (8, 301, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1165, 2084, 2956, 3883, 4754 }, HitEndTimes = new int[] { 1410, 2330, 3201, 4176, 5000 }, FileName = "MCV0301.ani" } },
        { (8, 302, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2420, 4210, 6260 }, HitEndTimes = new int[] { 2850, 4650, 6630 }, FileName = "MCV0302.ani" } },
        { (8, 303, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5950 }, HitEndTimes = new int[] { 6340 }, FileName = "MCV0303.ani" } },
        { (9, 302, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2520, 3320, 4120, 4850, 5700 }, HitEndTimes = new int[] { 2820, 3620, 4430, 5160, 6200 }, FileName = "MCH0302.ani" } },
        { (10, 301, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 3060, 4340, 5700, 6980 }, HitEndTimes = new int[] { 2070, 3420, 4700, 6130, 7340 }, FileName = "MCZ0301.ani" } },
        { (10, 303, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5950 }, HitEndTimes = new int[] { 6340 }, FileName = "MCZ0303.ani" } },
        { (6, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5230 }, HitEndTimes = new int[] { 7350 }, FileName = "MCD0201.ani" } },
        { (6, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6090 }, HitEndTimes = new int[] { 7270 }, FileName = "MCD0202.ani" } },
        { (6, 203, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3850 }, HitEndTimes = new int[] { 5230 }, FileName = "MCD0203.ani" } },
        { (6, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8640 }, HitEndTimes = new int[] { 1020, 9090 }, FileName = "MCD0212.ani" } },
        { (6, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5690 }, HitEndTimes = new int[] { 6410 }, FileName = "MCD0314.ani" } },
        { (6, 159, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 740 }, HitEndTimes = new int[] { 1360 }, FileName = "MCD0159.ani" } },
        { (6, 160, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCD0160.ani" } },
        { (6, 161, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCD0161.ani" } },
        { (6, 158, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3620 }, HitEndTimes = new int[] { 3840 }, FileName = "MCD0158.ani" } },
        { (6, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCD0150.ani" } },
        { (6, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCD0150.ani" } },
        { (6, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCD0150.ani" } },
        { (6, 207, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6060 }, HitEndTimes = new int[] { 6890 }, FileName = "AMCD0207.ani" } },
        { (6, 208, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5680 }, HitEndTimes = new int[] { 7040 }, FileName = "AMCD0208.ani" } },
        { (6, 209, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3630, 6740 }, HitEndTimes = new int[] { 4540, 7420 }, FileName = "AMCD0209.ani" } },
        { (6, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1560 }, HitEndTimes = new int[] { 3510 }, FileName = "AMCD0301.ani" } },
        { (6, 302, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2270, 5310 }, HitEndTimes = new int[] { 2660, 5620 }, FileName = "AMCD0302.ani" } },
        { (6, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4610 }, HitEndTimes = new int[] { 4920 }, FileName = "AMCD0303.ani" } },
        { (6, 305, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1670, 3710, 7190 }, HitEndTimes = new int[] { 2730, 5450, 8330 }, FileName = "AMCD0305.ani" } },
        { (6, 306, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6890 }, FileName = "AMCD0306.ani" } },
        { (6, 308, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6360 }, FileName = "AMCD0308.ani" } },
        { (6, 309, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6440 }, FileName = "AMCD0309.ani" } },
        { (6, 310, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5380 }, HitEndTimes = new int[] { 5980 }, FileName = "AMCD0310.ani" } },
        { (6, 312, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6290 }, FileName = "AMCD0312.ani" } },
        { (6, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6290 }, HitEndTimes = new int[] { 6970 }, FileName = "AMCD0313.ani" } },
        { (6, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCD0150.ani" } },
        { (6, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCD0150.ani" } },
        { (6, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCD0150.ani" } },
        { (6, 207, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3360 }, HitEndTimes = new int[] { 3830 }, FileName = "BMCD0207.ani" } },
        { (6, 208, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4690 }, HitEndTimes = new int[] { 5080 }, FileName = "BMCD0208.ani" } },
        { (6, 209, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3750, 6880 }, HitEndTimes = new int[] { 5000, 8050 }, FileName = "BMCD0209.ani" } },
        { (6, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5380 }, HitEndTimes = new int[] { 6210 }, FileName = "BMCD0301.ani" } },
        { (6, 303, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6060 }, HitEndTimes = new int[] { 6890 }, FileName = "BMCD0303.ani" } },
        { (6, 304, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7120 }, HitEndTimes = new int[] { 8480 }, FileName = "BMCD0304.ani" } },
        { (6, 306, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1890, 3410, 6970 }, HitEndTimes = new int[] { 2270, 3860, 7350 }, FileName = "BMCD0306.ani" } },
        { (6, 307, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7800 }, HitEndTimes = new int[] { 8180 }, FileName = "BMCD0307.ani" } },
        { (6, 309, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1720, 3200, 6480 }, HitEndTimes = new int[] { 2110, 3510, 6720 }, FileName = "BMCD0309.ani" } },
        { (6, 310, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4010 }, HitEndTimes = new int[] { 4850 }, FileName = "BMCD0310.ani" } },
        { (6, 312, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1970, 4160, 7880 }, HitEndTimes = new int[] { 2350, 4390, 8330 }, FileName = "BMCD0312.ani" } },
        { (6, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5300 }, HitEndTimes = new int[] { 5680 }, FileName = "BMCD0313.ani" } },
        { (6, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCD0150.ani" } },
        { (6, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCD0150.ani" } },
        { (6, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5070 }, HitEndTimes = new int[] { 6290 }, FileName = "AMCD0315.ani" } },
        { (6, 316, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6042 }, HitEndTimes = new int[] { 7141 }, FileName = "AMCD0316.ani" } },
        { (6, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5250 }, HitEndTimes = new int[] { 6080 }, FileName = "AMCD0317.ani" } },
        { (6, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "AMCD0319.ani" } },
        { (6, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5534 }, FileName = "AMCD0320.ani" } },
        { (6, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5180 }, HitEndTimes = new int[] { 6910 }, FileName = "AMCD0326.ani" } },
        { (6, 331, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5230 }, HitEndTimes = new int[] { 5830 }, FileName = "AMCD0331.ani" } },
        { (6, 339, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3120, 5650 }, HitEndTimes = new int[] { 4060, 7030 }, FileName = "AMCD0339.ani" } },
        { (6, 315, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2420 }, HitEndTimes = new int[] { 3330 }, FileName = "BMCD0315.ani" } },
        { (6, 316, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2638, 4366 }, HitEndTimes = new int[] { 3389, 5170 }, FileName = "BMCD0316.ani" } },
        { (6, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3260 }, HitEndTimes = new int[] { 4740 }, FileName = "BMCD0317.ani" } },
        { (6, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2911 }, HitEndTimes = new int[] { 3836 }, FileName = "BMCD0318.ani" } },
        { (6, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6020 }, HitEndTimes = new int[] { 6660 }, FileName = "BMCD0319.ani" } },
        { (6, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5180 }, HitEndTimes = new int[] { 6910 }, FileName = "BMCD0326.ani" } },
        { (6, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4564 }, HitEndTimes = new int[] { 5776 }, FileName = "BMCD0327.ani" } },
        { (6, 339, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3120, 5650 }, HitEndTimes = new int[] { 4060, 7030 }, FileName = "BMCD0339.ani" } },
        { (7, 301, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3300 }, HitEndTimes = new int[] { 3830 }, FileName = "MCM0301.ani" } },
        { (7, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 7360 }, FileName = "MCM0202.ani" } },
        { (7, 203, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 7360 }, FileName = "MCM0203.ani" } },
        { (7, 204, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 7360 }, FileName = "MCM0204.ani" } },
        { (7, 210, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4860 }, HitEndTimes = new int[] { 5440 }, FileName = "MCM0210.ani" } },
        { (7, 211, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6020 }, HitEndTimes = new int[] { 7100 }, FileName = "MCM0211.ani" } },
        { (7, 212, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5630 }, HitEndTimes = new int[] { 5700 }, FileName = "MCM0212.ani" } },
        { (7, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "MCM0201.ani" } },
        { (7, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "MCM0201.ani" } },
        { (7, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "MCM0201.ani" } },
        { (7, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "MCM0201.ani" } },
        { (7, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "MCM0201.ani" } },
        { (7, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "MCM0201.ani" } },
        { (7, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "MCM0201.ani" } },
        { (7, 207, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "AMCM0207.ani" } },
        { (7, 208, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "AMCM0208.ani" } },
        { (7, 300, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "AMCM0300.ani" } },
        { (7, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "AMCM0301.ani" } },
        { (7, 302, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "AMCM0302.ani" } },
        { (7, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "AMCM0303.ani" } },
        { (7, 305, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "AMCM0305.ani" } },
        { (7, 306, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "AMCM0306.ani" } },
        { (7, 307, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "AMCM0307.ani" } },
        { (7, 310, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6980 }, HitEndTimes = new int[] { 7170 }, FileName = "AMCM0310.ani" } },
        { (7, 311, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5310 }, HitEndTimes = new int[] { 5630 }, FileName = "AMCM0311.ani" } },
        { (7, 312, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5310 }, HitEndTimes = new int[] { 5630 }, FileName = "AMCM0312.ani" } },
        { (7, 206, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "BMCM0206.ani" } },
        { (7, 207, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "BMCM0207.ani" } },
        { (7, 209, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "BMCM0209.ani" } },
        { (7, 300, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "BMCM0300.ani" } },
        { (7, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "BMCM0301.ani" } },
        { (7, 302, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "BMCM0302.ani" } },
        { (7, 304, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "BMCM0304.ani" } },
        { (7, 305, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "BMCM0305.ani" } },
        { (7, 306, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "BMCM0306.ani" } },
        { (7, 308, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6660 }, HitEndTimes = new int[] { 6850 }, FileName = "BMCM0308.ani" } },
        { (7, 309, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6660 }, HitEndTimes = new int[] { 6850 }, FileName = "BMCM0309.ani" } },
        { (7, 310, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6660 }, HitEndTimes = new int[] { 6850 }, FileName = "BMCM0310.ani" } },
        { (7, 312, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5380 }, HitEndTimes = new int[] { 5760 }, FileName = "BMCM0312.ani" } },
        { (7, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4100 }, HitEndTimes = new int[] { 4610 }, FileName = "BMCM0313.ani" } },
        { (7, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5723 }, FileName = "AMCM0315.ani" } },
        { (7, 316, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5723 }, FileName = "AMCM0316.ani" } },
        { (7, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5435 }, FileName = "AMCM0318.ani" } },
        { (7, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5678 }, HitEndTimes = new int[] { 5920 }, FileName = "AMCM0319.ani" } },
        { (7, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5678 }, HitEndTimes = new int[] { 5920 }, FileName = "AMCM0320.ani" } },
        { (7, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4852 }, HitEndTimes = new int[] { 5625 }, FileName = "AMCM0327.ani" } },
        { (7, 330, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5370 }, HitEndTimes = new int[] { 6580 }, FileName = "AMCM0330.ani" } },
        { (7, 331, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5550 }, HitEndTimes = new int[] { 6420 }, FileName = "AMCM0331.ani" } },
        { (7, 339, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1000, 3600 }, HitEndTimes = new int[] { 3600, 6200 }, FileName = "AMCM0339.ani" } },
        { (7, 342, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2100, 3200, 5600 }, HitEndTimes = new int[] { 2250, 3350, 5750 }, FileName = "AMCM0342.ani" } },
        { (7, 315, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3737 }, HitEndTimes = new int[] { 4564 }, FileName = "BMCM0315.ani" } },
        { (7, 316, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3737 }, HitEndTimes = new int[] { 4564 }, FileName = "BMCM0316.ani" } },
        { (7, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5625 }, FileName = "BMCM0317.ani" } },
        { (7, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5094 }, HitEndTimes = new int[] { 5723 }, FileName = "BMCM0319.ani" } },
        { (7, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5094 }, HitEndTimes = new int[] { 5723 }, FileName = "BMCM0320.ani" } },
        { (7, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4122 }, HitEndTimes = new int[] { 5333 }, FileName = "BMCM0326.ani" } },
        { (7, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5370 }, HitEndTimes = new int[] { 6580 }, FileName = "BMCM0330.ani" } },
        { (7, 331, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5550 }, HitEndTimes = new int[] { 6420 }, FileName = "BMCM0331.ani" } },
        { (7, 338, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5300 }, HitEndTimes = new int[] { 6270 }, FileName = "BMCM0338.ani" } },
        { (7, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5300 }, HitEndTimes = new int[] { 5450 }, FileName = "BMCM0341.ani" } },
        { (7, 342, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1900, 3100, 5450 }, HitEndTimes = new int[] { 2050, 3250, 5600 }, FileName = "BMCM0342.ani" } },
        { (8, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3230 }, HitEndTimes = new int[] { 3640 }, FileName = "MCV0201.ani" } },
        { (8, 203, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1320, 2750, 5630 }, HitEndTimes = new int[] { 1740, 3110, 6050 }, FileName = "MCV0203.ani" } },
        { (8, 205, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1910, 2940, 5320 }, HitEndTimes = new int[] { 2320, 3300, 5730 }, FileName = "MCV0205.ani" } },
        { (8, 211, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4400 }, HitEndTimes = new int[] { 4830 }, FileName = "MCV0211.ani" } },
        { (8, 211, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4400 }, HitEndTimes = new int[] { 4830 }, FileName = "MCV0211.ani" } },
        { (8, 211, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4400 }, HitEndTimes = new int[] { 4830 }, FileName = "MCV0211.ani" } },
        { (8, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2370, 4970 }, HitEndTimes = new int[] { 2770, 5380 }, FileName = "MCV0212.ani" } },
        { (8, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2370, 4970 }, HitEndTimes = new int[] { 2770, 5380 }, FileName = "MCV0212.ani" } },
        { (8, 217, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5810 }, HitEndTimes = new int[] { 6230 }, FileName = "MCV0217.ani" } },
        { (8, 219, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2580 }, HitEndTimes = new int[] { 3280 }, FileName = "MCV0219.ani" } },
        { (8, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6720 }, HitEndTimes = new int[] { 7040 }, FileName = "MCV0314.ani" } },
        { (8, 213, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 3060, 4340, 5700, 6980 }, HitEndTimes = new int[] { 2070, 3420, 4700, 6130, 7340 }, FileName = "MCV0213.ani" } },
        { (8, 214, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3840 }, HitEndTimes = new int[] { 5060 }, FileName = "MCV0214.ani" } },
        { (8, 214, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3840 }, HitEndTimes = new int[] { 5060 }, FileName = "MCV0214.ani" } },
        { (8, 217, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5810 }, HitEndTimes = new int[] { 6230 }, FileName = "MCV0217.ani" } },
        { (8, 218, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5450, 6190, 7560 }, HitEndTimes = new int[] { 5950, 6750, 8240 }, FileName = "MCV0218.ani" } },
        { (8, 225, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1260, 2200, 3100, 4080, 5100 }, HitEndTimes = new int[] { 1330, 2310, 3220, 4160, 5180 }, FileName = "MCV0225.ani" } },
        { (8, 227, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCV0227.ani" } },
        { (8, 228, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCV0228.ani" } },
        { (8, 229, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8300 }, HitEndTimes = new int[] { 8630 }, FileName = "MCV0229.ani" } },
        { (8, 229, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8300 }, HitEndTimes = new int[] { 8630 }, FileName = "MCV0229.ani" } },
        { (8, 229, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8300 }, HitEndTimes = new int[] { 8630 }, FileName = "MCV0229.ani" } },
        { (8, 230, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCV0230.ani" } },
        { (8, 307, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7350 }, HitEndTimes = new int[] { 7740 }, FileName = "BMCV0307.ani" } },
        { (8, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6720 }, HitEndTimes = new int[] { 7040 }, FileName = "MCV0314.ani" } },
        { (8, 316, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4976, 5551, 6223 }, HitEndTimes = new int[] { 5235, 5810, 6483 }, FileName = "MCV0316.ani" } },
        { (8, 309, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4045, 4465, 4898, 5290, 5724 }, HitEndTimes = new int[] { 4272, 4658, 5092, 5483, 5879 }, FileName = "MCV0309.ani" } },
        { (8, 311, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4850 }, HitEndTimes = new int[] { 5095 }, FileName = "MCV0311.ani" } },
        { (8, 312, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4850 }, HitEndTimes = new int[] { 5095 }, FileName = "MCV0312.ani" } },
        { (8, 310, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4045, 4465, 4898, 5290, 5724 }, HitEndTimes = new int[] { 4272, 4658, 5092, 5483, 5879 }, FileName = "MCV0310.ani" } },
        { (8, 312, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4850 }, HitEndTimes = new int[] { 5095 }, FileName = "MCV0312.ani" } },
        { (8, 301, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1165, 2084, 2956, 3883, 4754 }, HitEndTimes = new int[] { 1410, 2330, 3201, 4176, 5000 }, FileName = "MCV0301.ani" } },
        { (8, 326, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4512 }, HitEndTimes = new int[] { 4850 }, FileName = "MCV0326.ani" } },
        { (8, 330, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5090 }, HitEndTimes = new int[] { 6150 }, FileName = "MCV0330.ani" } },
        { (8, 331, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3720 }, HitEndTimes = new int[] { 5230 }, FileName = "MCV0331.ani" } },
        { (8, 332, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2820 }, HitEndTimes = new int[] { 4000 }, FileName = "MCV0332.ani" } },
        { (9, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4770 }, HitEndTimes = new int[] { 5170 }, FileName = "MCH0201.ani" } },
        { (9, 202, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 4650, 5550, 6400, 7200 }, HitEndTimes = new int[] { 4950, 5850, 6700, 7500 }, FileName = "MCH0202.ani" } },
        { (9, 203, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1440, 2720, 3360, 4800, 6100 }, HitEndTimes = new int[] { 1840, 3120, 3760, 5400, 6500 }, FileName = "MCH0203.ani" } },
        { (9, 205, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 7350, 8300, 9300 }, HitEndTimes = new int[] { 7700, 8630, 9650 }, FileName = "MCH0205.ani" } },
        { (9, 205, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 7350, 8300, 9300 }, HitEndTimes = new int[] { 7700, 8630, 9650 }, FileName = "MCH0205.ani" } },
        { (9, 206, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1567, 2964, 4243 }, HitEndTimes = new int[] { 1802, 3199, 4556 }, FileName = "MCH0206.ani" } },
        { (9, 206, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1567, 2964, 4243 }, HitEndTimes = new int[] { 1802, 3199, 4556 }, FileName = "MCH0206.ani" } },
        { (9, 208, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7500 }, HitEndTimes = new int[] { 7940 }, FileName = "MCH0208.ani" } },
        { (9, 209, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1797, 2765, 4458 }, HitEndTimes = new int[] { 2074, 2993, 4735 }, FileName = "MCH0209.ani" } },
        { (9, 209, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1797, 2765, 4458 }, HitEndTimes = new int[] { 2074, 2993, 4735 }, FileName = "MCH0209.ani" } },
        { (9, 210, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 3060, 4340, 5700, 6980 }, HitEndTimes = new int[] { 2070, 3420, 4700, 6130, 7340 }, FileName = "MCH0210.ani" } },
        { (9, 212, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2420, 4210, 6260 }, HitEndTimes = new int[] { 2850, 4650, 6630 }, FileName = "MCH0212.ani" } },
        { (9, 212, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2420, 4210, 6260 }, HitEndTimes = new int[] { 2850, 4650, 6630 }, FileName = "MCH0212.ani" } },
        { (9, 314, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3650, 4800, 6530 }, HitEndTimes = new int[] { 3900, 4990, 7040 }, FileName = "MCH0314.ani" } },
        { (9, 213, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2450 }, HitEndTimes = new int[] { 2850 }, FileName = "MCH0213.ani" } },
        { (9, 220, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCH0220.ani" } },
        { (9, 215, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2300 }, HitEndTimes = new int[] { 2760 }, FileName = "MCH0215.ani" } },
        { (9, 216, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2100, 3700 }, HitEndTimes = new int[] { 2450, 4200 }, FileName = "MCH0216.ani" } },
        { (9, 217, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4250, 5000 }, HitEndTimes = new int[] { 4600, 5400 }, FileName = "MCH0217.ani" } },
        { (9, 215, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2300 }, HitEndTimes = new int[] { 2760 }, FileName = "MCH0215.ani" } },
        { (9, 218, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1450 }, HitEndTimes = new int[] { 1800 }, FileName = "MCH0218.ani" } },
        { (9, 209, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1797, 2765, 4458 }, HitEndTimes = new int[] { 2074, 2993, 4735 }, FileName = "MCH0209.ani" } },
        { (9, 215, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2300 }, HitEndTimes = new int[] { 2760 }, FileName = "MCH0215.ani" } },
        { (9, 218, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1450 }, HitEndTimes = new int[] { 1800 }, FileName = "MCH0218.ani" } },
        { (9, 219, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2450 }, FileName = "MCH0219.ani" } },
        { (9, 205, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 7350, 8300, 9300 }, HitEndTimes = new int[] { 7700, 8630, 9650 }, FileName = "MCH0205.ani" } },
        { (9, 300, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6200 }, HitEndTimes = new int[] { 6750 }, FileName = "MCH0300.ani" } },
        { (9, 301, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4850, 6410, 8800 }, HitEndTimes = new int[] { 5160, 6800, 9200 }, FileName = "MCH0301.ani" } },
        { (9, 303, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5050 }, HitEndTimes = new int[] { 5690 }, FileName = "MCH0303.ani" } },
        { (9, 304, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4930, 6500 }, HitEndTimes = new int[] { 5300, 6900 }, FileName = "MCH0304.ani" } },
        { (9, 305, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 7280, 9300 }, HitEndTimes = new int[] { 7900, 9940 }, FileName = "MCH0305.ani" } },
        { (9, 305, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 7280, 9300 }, HitEndTimes = new int[] { 7900, 9940 }, FileName = "MCH0305.ani" } },
        { (9, 209, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1797, 2765, 4458 }, HitEndTimes = new int[] { 2074, 2993, 4735 }, FileName = "MCH0209.ani" } },
        { (9, 206, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1567, 2964, 4243 }, HitEndTimes = new int[] { 1802, 3199, 4556 }, FileName = "MCH0206.ani" } },
        { (9, 319, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3615 }, HitEndTimes = new int[] { 4218 }, FileName = "MCH0319.ani" } },
        { (9, 320, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2795, 3447, 4756 }, HitEndTimes = new int[] { 3152, 3785, 5433 }, FileName = "MCH0320.ani" } },
        { (9, 326, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4244 }, HitEndTimes = new int[] { 4899 }, FileName = "MCH0326.ani" } },
        { (9, 330, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6060 }, HitEndTimes = new int[] { 7370 }, FileName = "MCH0330.ani" } },
        { (9, 331, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5190 }, HitEndTimes = new int[] { 6550 }, FileName = "MCH0331.ani" } },
        { (9, 341, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5450 }, HitEndTimes = new int[] { 5600 }, FileName = "MCH0341.ani" } },
        { (10, 217, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1860, 3900 }, HitEndTimes = new int[] { 1960, 4000 }, FileName = "MCZ0217.ani" } },
        { (10, 218, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1340, 3860 }, HitEndTimes = new int[] { 1440, 3960 }, FileName = "MCZ0218.ani" } },
        { (10, 219, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1240, 2560 }, HitEndTimes = new int[] { 1340, 2660 }, FileName = "MCZ0219.ani" } },
        { (10, 221, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1670, 4350 }, HitEndTimes = new int[] { 1770, 4450 }, FileName = "MCZ0221.ani" } },
        { (10, 222, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2430, 6270 }, HitEndTimes = new int[] { 2530, 6370 }, FileName = "MCZ0222.ani" } },
        { (10, 203, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1790, 3820 }, HitEndTimes = new int[] { 1890, 3920 }, FileName = "MCZ0203.ani" } },
        { (10, 201, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1650, 2900, 4850 }, HitEndTimes = new int[] { 1700, 3000, 4950 }, FileName = "MCZ0201.ani" } },
        { (10, 210, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3670 }, HitEndTimes = new int[] { 3770 }, FileName = "MCZ0210.ani" } },
        { (10, 212, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCZ0212.ani" } },
        { (10, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3700 }, HitEndTimes = new int[] { 3800 }, FileName = "MCZ0314.ani" } },
        { (10, 204, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1700 }, HitEndTimes = new int[] { 2500 }, FileName = "MCZ0204.ani" } },
        { (10, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2100 }, HitEndTimes = new int[] { 3150 }, FileName = "MCZ0205.ani" } },
        { (10, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCZ0150.ani" } },
        { (10, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCZ0150.ani" } },
        { (10, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCZ0150.ani" } },
        { (10, 208, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2590, 3000, 5280, 6520, 8440 }, HitEndTimes = new int[] { 2690, 3100, 5300, 6620, 8540 }, FileName = "AMCZ0208.ani" } },
        { (10, 209, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3760 }, HitEndTimes = new int[] { 3860 }, FileName = "AMCZ0209.ani" } },
        { (10, 301, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4000 }, HitEndTimes = new int[] { 4100 }, FileName = "AMCZ0301.ani" } },
        { (10, 303, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 1150, 2430, 3970, 6500 }, HitEndTimes = new int[] { 1250, 2530, 4070, 6600 }, FileName = "AMCZ0303.ani" } },
        { (10, 304, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1770, 3450 }, HitEndTimes = new int[] { 1870, 3550 }, FileName = "AMCZ0304.ani" } },
        { (10, 305, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2430, 4230, 5420 }, HitEndTimes = new int[] { 2530, 4330, 5520 }, FileName = "AMCZ0305.ani" } },
        { (10, 307, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 500, 1900, 5570 }, HitEndTimes = new int[] { 600, 2000, 5670 }, FileName = "AMCZ0307.ani" } },
        { (10, 309, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2630, 4130, 6170, 7720, 10840 }, HitEndTimes = new int[] { 2730, 4230, 6270, 7820, 11840 }, FileName = "AMCZ0309.ani" } },
        { (10, 310, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1300, 4400 }, HitEndTimes = new int[] { 1400, 4500 }, FileName = "AMCZ0310.ani" } },
        { (10, 312, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2080, 2430, 2890, 3240, 3930 }, HitEndTimes = new int[] { 2180, 2530, 2990, 3340, 4030 }, FileName = "AMCZ0312.ani" } },
        { (10, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCZ0150.ani" } },
        { (10, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCZ0150.ani" } },
        { (10, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCZ0150.ani" } },
        { (10, 207, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2220, 2690, 3950, 5210, 6300 }, HitEndTimes = new int[] { 2270, 2740, 4000, 5260, 6350 }, FileName = "BMCZ0207.ani" } },
        { (10, 209, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3760 }, HitEndTimes = new int[] { 3860 }, FileName = "BMCZ0209.ani" } },
        { (10, 300, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1750, 4200 }, HitEndTimes = new int[] { 1850, 4300 }, FileName = "BMCZ0300.ani" } },
        { (10, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4000 }, HitEndTimes = new int[] { 4100 }, FileName = "BMCZ0301.ani" } },
        { (10, 303, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 1150, 2430, 3970, 6500 }, HitEndTimes = new int[] { 1250, 2530, 4070, 6600 }, FileName = "BMCZ0303.ani" } },
        { (10, 304, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1770, 3450 }, HitEndTimes = new int[] { 1870, 3550 }, FileName = "BMCZ0304.ani" } },
        { (10, 305, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2430, 4230, 5420 }, HitEndTimes = new int[] { 2530, 4330, 5520 }, FileName = "BMCZ0305.ani" } },
        { (10, 307, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 500, 1900, 5570 }, HitEndTimes = new int[] { 600, 2000, 5670 }, FileName = "BMCZ0307.ani" } },
        { (10, 309, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2630, 4130, 6170, 7720, 10840 }, HitEndTimes = new int[] { 2730, 4230, 6270, 7820, 11840 }, FileName = "BMCZ0309.ani" } },
        { (10, 310, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1300, 4400 }, HitEndTimes = new int[] { 1400, 4500 }, FileName = "BMCZ0310.ani" } },
        { (10, 312, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2080, 2430, 2890, 3240, 3930 }, HitEndTimes = new int[] { 2180, 2530, 2990, 3340, 4030 }, FileName = "BMCZ0312.ani" } },
        { (10, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCZ0150.ani" } },
        { (10, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCZ0150.ani" } },
        { (10, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCZ0150.ani" } },
        { (10, 317, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 844, 1687, 2428, 3055, 4320 }, HitEndTimes = new int[] { 904, 1747, 2489, 3115, 4381 }, FileName = "AMCZ0317.ani" } },
        { (10, 319, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 586, 1564, 2466, 3930 }, HitEndTimes = new int[] { 663, 1641, 2543, 4007 }, FileName = "AMCZ0319.ani" } },
        { (10, 326, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1330, 4080 }, HitEndTimes = new int[] { 1430, 4180 }, FileName = "AMCZ0326.ani" } },
        { (10, 327, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2444, 4183 }, HitEndTimes = new int[] { 2538, 4277 }, FileName = "AMCZ0327.ani" } },
        { (10, 331, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3350, 4550 }, HitEndTimes = new int[] { 3540, 5320 }, FileName = "AMCZ0331.ani" } },
        { (10, 338, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3880, 5450, 6530 }, HitEndTimes = new int[] { 4500, 5740, 6870 }, FileName = "AMCZ0338.ani" } },
        { (10, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5350 }, HitEndTimes = new int[] { 5500 }, FileName = "AMCZ0341.ani" } },
        { (10, 315, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 1211, 2186, 3397, 4999 }, HitEndTimes = new int[] { 1282, 2257, 3468, 5070 }, FileName = "BMCZ0315.ani" } },
        { (10, 316, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4759 }, HitEndTimes = new int[] { 4843 }, FileName = "BMCZ0316.ani" } },
        { (10, 317, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 844, 1687, 2428, 3055, 4320 }, HitEndTimes = new int[] { 904, 1747, 2489, 3115, 4381 }, FileName = "BMCZ0317.ani" } },
        { (10, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3878 }, HitEndTimes = new int[] { 3961 }, FileName = "BMCZ0318.ani" } },
        { (10, 320, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 924, 1945, 2553, 3104, 5147 }, HitEndTimes = new int[] { 1005, 2026, 2634, 3185, 5228 }, FileName = "BMCZ0320.ani" } },
        { (10, 327, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2444, 4183 }, HitEndTimes = new int[] { 2538, 4277 }, FileName = "BMCZ0327.ani" } },
        { (10, 330, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2830, 5550 }, HitEndTimes = new int[] { 3060, 6000 }, FileName = "BMCZ0330.ani" } },
        { (10, 338, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3880, 5450, 6530 }, HitEndTimes = new int[] { 4500, 5740, 6870 }, FileName = "BMCZ0338.ani" } },
        { (10, 339, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2280, 3350, 5860 }, HitEndTimes = new int[] { 3010, 4460, 6580 }, FileName = "BMCZ0339.ani" } },
        { (10, 342, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4650, 4800, 4950 }, HitEndTimes = new int[] { 4800, 4950, 5100 }, FileName = "BMCZ0342.ani" } },
        { (11, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7040 }, HitEndTimes = new int[] { 8190 }, FileName = "MCU0314.ani" } },
        { (11, 402, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1220 }, HitEndTimes = new int[] { 2750 }, FileName = "MCU0402.ani" } },
        { (11, 203, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1450 }, HitEndTimes = new int[] { 4660 }, FileName = "MCU0203.ani" } },
        { (11, 204, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4400 }, HitEndTimes = new int[] { 6640 }, FileName = "MCU0204.ani" } },
        { (11, 404, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1830 }, HitEndTimes = new int[] { 3090 }, FileName = "MCU0404.ani" } },
        { (11, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1540 }, HitEndTimes = new int[] { 2460 }, FileName = "MCU0205.ani" } },
        { (11, 230, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCU0230.ani" } },
        { (11, 315, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 650, 2300, 5000 }, HitEndTimes = new int[] { 1200, 2950, 5300 }, FileName = "MCU0315.ani" } },
        { (12, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 8550 }, HitEndTimes = new int[] { 10030 }, FileName = "MCO0201.ani" } },
        { (12, 203, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 6240, 7510, 9440 }, HitEndTimes = new int[] { 6760, 8030, 10260 }, FileName = "MCO0203.ani" } },
        { (12, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1150, 7550 }, HitEndTimes = new int[] { 1280, 8130 }, FileName = "MCO0210.ani" } },
        { (12, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 7420 }, HitEndTimes = new int[] { 1340, 7870 }, FileName = "MCO0212.ani" } },
        { (12, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6400 }, HitEndTimes = new int[] { 6980 }, FileName = "MCO0314.ani" } },
        { (12, 207, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6050 }, HitEndTimes = new int[] { 6950 }, FileName = "MCO0207.ani" } },
        { (12, 208, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 4930 }, HitEndTimes = new int[] { 2620, 5950 }, FileName = "MCO0208.ani" } },
        { (12, 209, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2230, 6040 }, HitEndTimes = new int[] { 2690, 6310 }, FileName = "MCO0209.ani" } },
        { (12, 320, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6270 }, HitEndTimes = new int[] { 6590 }, FileName = "MCO0320.ani" } },
        { (12, 326, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6500 }, HitEndTimes = new int[] { 7740 }, FileName = "MCO0326.ani" } },
        { (12, 327, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5180 }, HitEndTimes = new int[] { 6820 }, FileName = "MCO0327.ani" } },
        { (13, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 830, 8640 }, HitEndTimes = new int[] { 1410, 9020 }, FileName = "MCG0210.ani" } },
        { (13, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8580 }, HitEndTimes = new int[] { 1100, 9150 }, FileName = "MCG0211.ani" } },
        { (13, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 7940 }, FileName = "MCG0314.ani" } },
        { (13, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5700 }, HitEndTimes = new int[] { 6500 }, FileName = "MCG0205.ani" } },
        { (13, 209, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5430 }, HitEndTimes = new int[] { 7230 }, FileName = "MCG0209.ani" } },
        { (13, 213, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3980 }, HitEndTimes = new int[] { 5000 }, FileName = "MCG0213.ani" } },
        { (13, 215, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3780 }, HitEndTimes = new int[] { 4120 }, FileName = "MCG0215.ani" } },
        { (13, 216, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4900 }, HitEndTimes = new int[] { 5300 }, FileName = "MCG0216.ani" } },
        { (13, 218, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4950 }, HitEndTimes = new int[] { 7330 }, FileName = "MCG0218.ani" } },
        { (13, 219, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4000 }, HitEndTimes = new int[] { 4500 }, FileName = "MCG0219.ani" } },
        { (13, 220, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3800 }, HitEndTimes = new int[] { 4690 }, FileName = "MCG0220.ani" } },
        { (13, 222, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2500 }, HitEndTimes = new int[] { 3150 }, FileName = "MCG0222.ani" } },
        { (13, 223, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 5200, 5500, 6000, 6500 }, HitEndTimes = new int[] { 5900, 6200, 6600, 8000 }, FileName = "MCG0223.ani" } },
        { (13, 300, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5000 }, HitEndTimes = new int[] { 5500 }, FileName = "MCG0300.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 302, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5000 }, HitEndTimes = new int[] { 6000 }, FileName = "MCG0302.ani" } },
        { (13, 304, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5000 }, HitEndTimes = new int[] { 6000 }, FileName = "MCG0304.ani" } },
        { (13, 305, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3800 }, HitEndTimes = new int[] { 4800 }, FileName = "MCG0305.ani" } },
        { (13, 306, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5090 }, HitEndTimes = new int[] { 6000 }, FileName = "MCG0306.ani" } },
        { (13, 307, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 3510, 4010, 4510, 5010, 5510 }, HitEndTimes = new int[] { 4510, 5010, 5510, 6010, 6800 }, FileName = "MCG0307.ani" } },
        { (13, 308, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2470, 3640, 6160 }, HitEndTimes = new int[] { 3400, 4600, 7100 }, FileName = "MCG0308.ani" } },
        { (13, 310, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3880 }, HitEndTimes = new int[] { 4600 }, FileName = "MCG0310.ani" } },
        { (13, 311, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5240 }, HitEndTimes = new int[] { 6200 }, FileName = "MCG0311.ani" } },
        { (13, 312, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6110 }, HitEndTimes = new int[] { 7100 }, FileName = "MCG0312.ani" } },
        { (13, 313, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4000, 4600, 5200 }, HitEndTimes = new int[] { 4600, 5200, 6210 }, FileName = "MCG0313.ani" } },
        { (13, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5570 }, HitEndTimes = new int[] { 7940 }, FileName = "MCG0314.ani" } },
        { (13, 315, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4500 }, HitEndTimes = new int[] { 6000 }, FileName = "MCG0315.ani" } },
        { (13, 316, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5090, 5500, 6000 }, HitEndTimes = new int[] { 6000, 6500, 7000 }, FileName = "MCG0316.ani" } },
        { (13, 317, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4710 }, HitEndTimes = new int[] { 5700 }, FileName = "MCG0317.ani" } },
        { (13, 318, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4510 }, HitEndTimes = new int[] { 5500 }, FileName = "MCG0318.ani" } },
        { (13, 342, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5450 }, HitEndTimes = new int[] { 5600 }, FileName = "MCG0342.ani" } },
        { (13, 341, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5800 }, HitEndTimes = new int[] { 5950 }, FileName = "MCG0341.ani" } },
        { (13, 343, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5450 }, HitEndTimes = new int[] { 5600 }, FileName = "MCG0343.ani" } },
        { (1, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2490 }, HitEndTimes = new int[] { 6650 }, FileName = "MCK0201.ani" } },
        { (1, 202, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2750, 7040 }, HitEndTimes = new int[] { 3650, 7360 }, FileName = "MCK0202.ani" } },
        { (1, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1980, 6720 }, HitEndTimes = new int[] { 2560, 7170 }, FileName = "MCK0211.ani" } },
        { (1, 206, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4770 }, HitEndTimes = new int[] { 5540 }, FileName = "AMCK0206.ani" } },
        { (1, 209, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7940 }, HitEndTimes = new int[] { 8770 }, FileName = "AMCK0209.ani" } },
        { (1, 302, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1660, 4160, 7740 }, HitEndTimes = new int[] { 2820, 5570, 9150 }, FileName = "AMCK0302.ani" } },
        { (1, 306, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1540, 4420, 8260 }, HitEndTimes = new int[] { 2820, 5700, 9090 }, FileName = "AMCK0306.ani" } },
        { (1, 310, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4660 }, HitEndTimes = new int[] { 5650 }, FileName = "AMCK0310.ani" } },
        { (1, 312, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3650, 6460 }, HitEndTimes = new int[] { 6020, 8960 }, FileName = "AMCK0312.ani" } },
        { (1, 208, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3490, 4290, 5150 }, HitEndTimes = new int[] { 3860, 4460, 5370 }, FileName = "BMCK0208.ani" } },
        { (1, 301, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2370, 6140 }, HitEndTimes = new int[] { 2880, 6660 }, FileName = "BMCK0301.ani" } },
        { (1, 305, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1790, 4100, 8380 }, HitEndTimes = new int[] { 3260, 5570, 9280 }, FileName = "BMCK0305.ani" } },
        { (1, 306, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3840, 5180, 6720 }, HitEndTimes = new int[] { 4610, 5950, 8060 }, FileName = "BMCK0306.ani" } },
        { (1, 310, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4140 }, HitEndTimes = new int[] { 5780 }, FileName = "BMCK0310.ani" } },
        { (1, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5500 }, HitEndTimes = new int[] { 9070 }, FileName = "BMCK0313.ani" } },
        { (1, 316, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1433, 2987 }, HitEndTimes = new int[] { 2183, 3617 }, FileName = "AMCK0316.ani" } },
        { (1, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3836 }, HitEndTimes = new int[] { 4222 }, FileName = "AMCK0326.ani" } },
        { (1, 331, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1540, 2740, 4900 }, HitEndTimes = new int[] { 2180, 3090, 6900 }, FileName = "AMCK0331.ani" } },
        { (1, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5330 }, HitEndTimes = new int[] { 5480 }, FileName = "AMCK0341.ani" } },
        { (1, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4321 }, HitEndTimes = new int[] { 4564 }, FileName = "BMCK0320.ani" } },
        { (1, 331, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1540, 2740, 4900 }, HitEndTimes = new int[] { 2180, 3090, 6900 }, FileName = "BMCK0331.ani" } },
        { (1, 339, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5000, 5500, 6000 }, HitEndTimes = new int[] { 5500, 6000, 6500 }, FileName = "BMCK0339.ani" } },
        { (1, 342, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4900, 6700 }, HitEndTimes = new int[] { 5050, 6850 }, FileName = "BMCK0342.ani" } },
        { (2, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2560, 8510 }, HitEndTimes = new int[] { 2880, 8960 }, FileName = "MCS0212.ani" } },
        { (2, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCS0150.ani" } },
        { (2, 209, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5250, 6200 }, HitEndTimes = new int[] { 5790, 7020 }, FileName = "AMCS0209.ani" } },
        { (2, 302, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4100, 5250, 7170 }, HitEndTimes = new int[] { 5120, 6850, 9340 }, FileName = "AMCS0302.ani" } },
        { (2, 306, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1540, 2880, 7620 }, HitEndTimes = new int[] { 2370, 4990, 9220 }, FileName = "AMCS0306.ani" } },
        { (2, 309, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 460, 3280, 5980 }, HitEndTimes = new int[] { 1050, 3680, 6310 }, FileName = "AMCS0309.ani" } },
        { (2, 312, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3840, 7420 }, HitEndTimes = new int[] { 6660, 9280 }, FileName = "AMCS0312.ani" } },
        { (2, 206, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5450, 6190, 7560 }, HitEndTimes = new int[] { 5950, 6750, 8240 }, FileName = "BMCS0206.ani" } },
        { (2, 209, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1260, 7240 }, HitEndTimes = new int[] { 1800, 7720 }, FileName = "BMCS0209.ani" } },
        { (2, 301, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5180 }, HitEndTimes = new int[] { 5890 }, FileName = "BMCS0301.ani" } },
        { (2, 304, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7680 }, HitEndTimes = new int[] { 9280 }, FileName = "BMCS0304.ani" } },
        { (2, 307, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1660, 4420, 6660 }, HitEndTimes = new int[] { 2820, 5890, 9020 }, FileName = "BMCS0307.ani" } },
        { (2, 309, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1130, 2490, 4210 }, HitEndTimes = new int[] { 1780, 3740, 4810 }, FileName = "BMCS0309.ani" } },
        { (2, 313, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7170 }, HitEndTimes = new int[] { 9220 }, FileName = "BMCS0313.ani" } },
        { (2, 316, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2812, 4480 }, HitEndTimes = new int[] { 3275, 4768 }, FileName = "AMCS0316.ani" } },
        { (2, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3976 }, HitEndTimes = new int[] { 5292 }, FileName = "AMCS0326.ani" } },
        { (2, 331, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2630, 3760, 5160 }, HitEndTimes = new int[] { 3060, 4090, 5790 }, FileName = "AMCS0331.ani" } },
        { (2, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 5910 }, FileName = "AMCS0341.ani" } },
        { (2, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4465 }, HitEndTimes = new int[] { 4806 }, FileName = "BMCS0320.ani" } },
        { (2, 326, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3976 }, HitEndTimes = new int[] { 5292 }, FileName = "BMCS0326.ani" } },
        { (2, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3010 }, HitEndTimes = new int[] { 5090 }, FileName = "BMCS0330.ani" } },
        { (2, 339, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2930, 4720 }, HitEndTimes = new int[] { 3550, 6730 }, FileName = "BMCS0339.ani" } },
        { (3, 202, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3120, 8770 }, HitEndTimes = new int[] { 4460, 9590 }, FileName = "MCB0202.ani" } },
        { (3, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 7680 }, HitEndTimes = new int[] { 1730, 8060 }, FileName = "MCB0211.ani" } },
        { (3, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCB0150.ani" } },
        { (3, 208, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 4930 }, HitEndTimes = new int[] { 2620, 5950 }, FileName = "AMCB0208.ani" } },
        { (3, 301, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2630, 5030 }, HitEndTimes = new int[] { 3230, 5330 }, FileName = "AMCB0301.ani" } },
        { (3, 304, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2220, 6110 }, HitEndTimes = new int[] { 4970, 8560 }, FileName = "AMCB0304.ani" } },
        { (3, 307, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2300, 4480, 7040 }, HitEndTimes = new int[] { 3390, 6340, 9090 }, FileName = "AMCB0307.ani" } },
        { (3, 309, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2370, 4680, 6580 }, HitEndTimes = new int[] { 2780, 5560, 6920 }, FileName = "AMCB0309.ani" } },
        { (3, 312, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1860, 4930 }, HitEndTimes = new int[] { 4290, 8770 }, FileName = "AMCB0312.ani" } },
        { (3, 150, 1), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "AMCB0150.ani" } },
        { (3, 207, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5390 }, HitEndTimes = new int[] { 6530 }, FileName = "BMCB0207.ani" } },
        { (3, 300, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 6410, 7180, 7960 }, HitEndTimes = new int[] { 6830, 7540, 8380 }, FileName = "BMCB0300.ani" } },
        { (3, 303, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4740, 6660 }, HitEndTimes = new int[] { 5950, 8900 }, FileName = "BMCB0303.ani" } },
        { (3, 306, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1980, 5570, 7810 }, HitEndTimes = new int[] { 4030, 7420, 9410 }, FileName = "BMCB0306.ani" } },
        { (3, 309, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2920, 7050 }, HitEndTimes = new int[] { 3800, 7530 }, FileName = "BMCB0309.ani" } },
        { (3, 312, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1980, 5120 }, HitEndTimes = new int[] { 4290, 8260 }, FileName = "BMCB0312.ani" } },
        { (3, 315, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3843 }, HitEndTimes = new int[] { 5397 }, FileName = "AMCB0315.ani" } },
        { (3, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4708 }, HitEndTimes = new int[] { 5920 }, FileName = "AMCB0319.ani" } },
        { (3, 331, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5530 }, HitEndTimes = new int[] { 6400 }, FileName = "AMCB0331.ani" } },
        { (3, 339, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4800, 5340, 5880 }, HitEndTimes = new int[] { 5340, 5880, 6420 }, FileName = "AMCB0339.ani" } },
        { (3, 315, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4420 }, HitEndTimes = new int[] { 4882 }, FileName = "BMCB0315.ani" } },
        { (3, 317, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2812 }, HitEndTimes = new int[] { 3881 }, FileName = "BMCB0317.ani" } },
        { (3, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4126 }, HitEndTimes = new int[] { 5433 }, FileName = "BMCB0327.ani" } },
        { (3, 338, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2600, 4500 }, HitEndTimes = new int[] { 4500, 6400 }, FileName = "BMCB0338.ani" } },
        { (3, 342, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2580, 5330 }, HitEndTimes = new int[] { 2730, 5480 }, FileName = "BMCB0342.ani" } },
        { (4, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 7810 }, HitEndTimes = new int[] { 380, 8700 }, FileName = "MCA0212.ani" } },
        { (4, 205, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1540 }, HitEndTimes = new int[] { 2460 }, FileName = "MCA0205.ani" } },
        { (4, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCA0150.ani" } },
        { (4, 207, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3280, 6640 }, HitEndTimes = new int[] { 4340, 7420 }, FileName = "AMCA0207.ani" } },
        { (4, 300, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1910, 6440 }, HitEndTimes = new int[] { 3350, 7230 }, FileName = "AMCA0300.ani" } },
        { (4, 302, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2170 }, HitEndTimes = new int[] { 4400 }, FileName = "AMCA0302.ani" } },
        { (4, 303, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4340 }, HitEndTimes = new int[] { 6700 }, FileName = "AMCA0303.ani" } },
        { (4, 307, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2170, 5190 }, HitEndTimes = new int[] { 3810, 7360 }, FileName = "AMCA0307.ani" } },
        { (4, 310, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3550, 5190, 6500 }, HitEndTimes = new int[] { 4070, 5720, 7230 }, FileName = "AMCA0310.ani" } },
        { (4, 312, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 900, 6980 }, HitEndTimes = new int[] { 2620, 7870 }, FileName = "AMCA0312.ani" } },
        { (4, 207, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2230, 6770 }, HitEndTimes = new int[] { 3280, 7160 }, FileName = "BMCA0207.ani" } },
        { (4, 301, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3550, 6770 }, HitEndTimes = new int[] { 4660, 7100 }, FileName = "BMCA0301.ani" } },
        { (4, 305, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2300 }, HitEndTimes = new int[] { 5520 }, FileName = "BMCA0305.ani" } },
        { (4, 308, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3020, 6960 }, HitEndTimes = new int[] { 4730, 8470 }, FileName = "BMCA0308.ani" } },
        { (4, 310, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 330, 3350, 6770 }, HitEndTimes = new int[] { 1770, 4870, 8210 }, FileName = "BMCA0310.ani" } },
        { (4, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCA0150.ani" } },
        { (4, 319, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4510 }, HitEndTimes = new int[] { 4655 }, FileName = "AMCA0319.ani" } },
        { (4, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6020 }, HitEndTimes = new int[] { 6590 }, FileName = "AMCA0327.ani" } },
        { (4, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4330 }, HitEndTimes = new int[] { 4490 }, FileName = "AMCA0341.ani" } },
        { (4, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3150 }, HitEndTimes = new int[] { 5430 }, FileName = "BMCA0330.ani" } },
        { (5, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 830, 8640 }, HitEndTimes = new int[] { 1410, 9020 }, FileName = "MCE0210.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4230 }, HitEndTimes = new int[] { 5600 }, FileName = "MCE0201.ani" } },
        { (5, 208, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 9160, 11480, 13790 }, HitEndTimes = new int[] { 9900, 12630, 14210 }, FileName = "AMCE0208.ani" } },
        { (5, 302, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 7000 }, HitEndTimes = new int[] { 8620 }, FileName = "AMCE0302.ani" } },
        { (5, 305, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5950 }, HitEndTimes = new int[] { 8260 }, FileName = "AMCE0305.ani" } },
        { (5, 309, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 850, 3880, 6570 }, HitEndTimes = new int[] { 1450, 4470, 7160 }, FileName = "AMCE0309.ani" } },
        { (5, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3420, 5320, 6310 }, HitEndTimes = new int[] { 4990, 6040, 8010 }, FileName = "AMCE0311.ani" } },
        { (5, 208, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 10260, 11450, 12080 }, HitEndTimes = new int[] { 10990, 11900, 12990 }, FileName = "BMCE0208.ani" } },
        { (5, 302, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5390 }, HitEndTimes = new int[] { 8080 }, FileName = "BMCE0302.ani" } },
        { (5, 303, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6710 }, HitEndTimes = new int[] { 8440 }, FileName = "BMCE0303.ani" } },
        { (5, 304, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2330, 4310, 6350 }, HitEndTimes = new int[] { 3950, 5990, 8320 }, FileName = "BMCE0304.ani" } },
        { (5, 308, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2980, 4540, 7050 }, HitEndTimes = new int[] { 3800, 5090, 8270 }, FileName = "BMCE0308.ani" } },
        { (5, 311, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1280, 4350, 7040 }, HitEndTimes = new int[] { 3010, 6530, 9150 }, FileName = "BMCE0311.ani" } },
        { (5, 316, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3055 }, HitEndTimes = new int[] { 3347 }, FileName = "AMCE0316.ani" } },
        { (5, 320, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4609 }, HitEndTimes = new int[] { 4897 }, FileName = "AMCE0320.ani" } },
        { (5, 331, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2090 }, HitEndTimes = new int[] { 6110 }, FileName = "AMCE0331.ani" } },
        { (5, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5850 }, HitEndTimes = new int[] { 6000 }, FileName = "AMCE0341.ani" } },
        { (5, 319, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4753 }, HitEndTimes = new int[] { 5140 }, FileName = "BMCE0319.ani" } },
        { (5, 330, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4120 }, HitEndTimes = new int[] { 5720 }, FileName = "BMCE0330.ani" } },
        { (5, 339, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2220, 5000 }, HitEndTimes = new int[] { 3800, 5850 }, FileName = "BMCE0339.ani" } },
        { (5, 342, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5150, 5300, 5450 }, HitEndTimes = new int[] { 5300, 5450, 5600 }, FileName = "BMCE0342.ani" } },
        { (9, 301, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4850, 6410, 8800 }, HitEndTimes = new int[] { 5160, 6800, 9200 }, FileName = "MCH0301.ani" } },
        { (9, 303, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5050 }, HitEndTimes = new int[] { 5690 }, FileName = "MCH0303.ani" } },
        { (10, 302, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2420, 4210, 6260 }, HitEndTimes = new int[] { 2850, 4650, 6630 }, FileName = "MCZ0302.ani" } },
        { (6, 210, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8380 }, HitEndTimes = new int[] { 900, 9020 }, FileName = "MCD0210.ani" } },
        { (6, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8900 }, HitEndTimes = new int[] { 1020, 9280 }, FileName = "MCD0211.ani" } },
        { (6, 162, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 2700 }, HitEndTimes = new int[] { 1410, 3200 }, FileName = "MCD0162.ani" } },
        { (6, 206, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5830 }, HitEndTimes = new int[] { 7040 }, FileName = "AMCD0206.ani" } },
        { (6, 300, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3030, 3710, 5680 }, HitEndTimes = new int[] { 3560, 4390, 6510 }, FileName = "AMCD0300.ani" } },
        { (6, 304, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1870 }, HitEndTimes = new int[] { 2110 }, FileName = "AMCD0304.ani" } },
        { (6, 307, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3330, 4770, 7400 }, HitEndTimes = new int[] { 4320, 6590, 8400 }, FileName = "AMCD0307.ani" } },
        { (6, 311, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3360, 4760, 7110 }, HitEndTimes = new int[] { 4220, 5620, 7970 }, FileName = "AMCD0311.ani" } },
        { (6, 206, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3510 }, HitEndTimes = new int[] { 4060 }, FileName = "BMCD0206.ani" } },
        { (6, 300, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2270, 3100, 6820 }, HitEndTimes = new int[] { 3560, 4390, 8330 }, FileName = "BMCD0300.ani" } },
        { (6, 302, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1590, 3180, 5910 }, HitEndTimes = new int[] { 2570, 4240, 6740 }, FileName = "BMCD0302.ani" } },
        { (6, 305, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4320 }, HitEndTimes = new int[] { 4850 }, FileName = "BMCD0305.ani" } },
        { (6, 308, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2810 }, HitEndTimes = new int[] { 2970 }, FileName = "BMCD0308.ani" } },
        { (6, 311, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1060, 2800, 6060 }, HitEndTimes = new int[] { 1890, 3560, 7040 }, FileName = "BMCD0311.ani" } },
        { (6, 150, 2), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "BMCD0150.ani" } },
        { (6, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3783 }, HitEndTimes = new int[] { 4465 }, FileName = "AMCD0318.ani" } },
        { (6, 327, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4564 }, HitEndTimes = new int[] { 5776 }, FileName = "AMCD0327.ani" } },
        { (6, 342, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2800, 4750 }, HitEndTimes = new int[] { 2950, 4900 }, FileName = "AMCD0342.ani" } },
        { (6, 320, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5140 }, HitEndTimes = new int[] { 5435 }, FileName = "BMCD0320.ani" } },
        { (6, 331, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5230 }, HitEndTimes = new int[] { 5830 }, FileName = "BMCD0331.ani" } },
        { (6, 342, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4900, 5050 }, HitEndTimes = new int[] { 5050, 5200 }, FileName = "BMCD0342.ani" } },
        { (7, 314, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4740 }, HitEndTimes = new int[] { 4860 }, FileName = "MCM0314.ani" } },
        { (7, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6210 }, HitEndTimes = new int[] { 6660 }, FileName = "MCM0201.ani" } },
        { (7, 206, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "AMCM0206.ani" } },
        { (7, 209, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4930 }, HitEndTimes = new int[] { 5760 }, FileName = "AMCM0209.ani" } },
        { (7, 304, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "AMCM0304.ani" } },
        { (7, 308, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6980 }, HitEndTimes = new int[] { 7170 }, FileName = "AMCM0308.ani" } },
        { (7, 309, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6980 }, HitEndTimes = new int[] { 7170 }, FileName = "AMCM0309.ani" } },
        { (7, 313, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4740 }, HitEndTimes = new int[] { 4860 }, FileName = "AMCM0313.ani" } },
        { (7, 208, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 5060, 6530 }, HitEndTimes = new int[] { 5890, 7300 }, FileName = "BMCM0208.ani" } },
        { (7, 303, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5760 }, HitEndTimes = new int[] { 6340 }, FileName = "BMCM0303.ani" } },
        { (7, 307, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6530 }, HitEndTimes = new int[] { 6910 }, FileName = "BMCM0307.ani" } },
        { (7, 311, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5380 }, HitEndTimes = new int[] { 5760 }, FileName = "BMCM0311.ani" } },
        { (7, 317, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5435 }, FileName = "AMCM0317.ani" } },
        { (7, 326, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4122 }, HitEndTimes = new int[] { 5333 }, FileName = "AMCM0326.ani" } },
        { (7, 338, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5300 }, HitEndTimes = new int[] { 6270 }, FileName = "AMCM0338.ani" } },
        { (7, 341, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5130 }, HitEndTimes = new int[] { 5280 }, FileName = "AMCM0341.ani" } },
        { (7, 318, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5238 }, HitEndTimes = new int[] { 5625 }, FileName = "BMCM0318.ani" } },
        { (7, 327, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4852 }, HitEndTimes = new int[] { 5625 }, FileName = "BMCM0327.ani" } },
        { (7, 339, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1000, 3600 }, HitEndTimes = new int[] { 3600, 6200 }, FileName = "BMCM0339.ani" } },
        { (8, 202, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1290, 2790 }, HitEndTimes = new int[] { 1650, 3140 }, FileName = "MCV0202.ani" } },
        { (8, 204, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 460, 2580, 3510, 5520 }, HitEndTimes = new int[] { 830, 2940, 3920, 5940 }, FileName = "MCV0204.ani" } },
        { (8, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2370, 4970 }, HitEndTimes = new int[] { 2770, 5380 }, FileName = "MCV0212.ani" } },
        { (8, 218, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 5450, 6190, 7560 }, HitEndTimes = new int[] { 5950, 6750, 8240 }, FileName = "MCV0218.ani" } },
        { (8, 213, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 3060, 4340, 5700, 6980 }, HitEndTimes = new int[] { 2070, 3420, 4700, 6130, 7340 }, FileName = "MCV0213.ani" } },
        { (8, 213, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 3060, 4340, 5700, 6980 }, HitEndTimes = new int[] { 2070, 3420, 4700, 6130, 7340 }, FileName = "MCV0213.ani" } },
        { (8, 216, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6410 }, HitEndTimes = new int[] { 7180 }, FileName = "MCV0216.ani" } },
        { (8, 219, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2580 }, HitEndTimes = new int[] { 3280 }, FileName = "MCV0219.ani" } },
        { (8, 226, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 300, 5030 }, HitEndTimes = new int[] { 600, 5210 }, FileName = "MCV0226.ani" } },
        { (8, 150, 0), new AnimationInfo { HitCount = 0, HitTimes = new int[] {  }, HitEndTimes = new int[] {  }, FileName = "MCV0150.ani" } },
        { (8, 315, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1410, 2138, 4806 }, HitEndTimes = new int[] { 1653, 2426, 5094 }, FileName = "MCV0315.ani" } },
        { (8, 308, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4045, 4465, 4898, 5290, 5724 }, HitEndTimes = new int[] { 4272, 4658, 5092, 5483, 5879 }, FileName = "MCV0308.ani" } },
        { (8, 310, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 4045, 4465, 4898, 5290, 5724 }, HitEndTimes = new int[] { 4272, 4658, 5092, 5483, 5879 }, FileName = "MCV0310.ani" } },
        { (8, 211, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4400 }, HitEndTimes = new int[] { 4830 }, FileName = "MCV0211.ani" } },
        { (8, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2370, 4970 }, HitEndTimes = new int[] { 2770, 5380 }, FileName = "MCV0212.ani" } },
        { (8, 341, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 5200 }, HitEndTimes = new int[] { 5350 }, FileName = "MCV0341.ani" } },
        { (8, 342, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3400, 4850 }, HitEndTimes = new int[] { 3550, 5000 }, FileName = "MCV0342.ani" } },
        { (9, 202, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 4650, 5550, 6400, 7200 }, HitEndTimes = new int[] { 4950, 5850, 6700, 7500 }, FileName = "MCH0202.ani" } },
        { (9, 204, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1440, 2720, 3360, 4800, 6100 }, HitEndTimes = new int[] { 1840, 3120, 3760, 5400, 6500 }, FileName = "MCH0204.ani" } },
        { (9, 205, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 7350, 8300, 9300 }, HitEndTimes = new int[] { 7700, 8630, 9650 }, FileName = "MCH0205.ani" } },
        { (9, 207, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 3721, 4348, 4974, 5640 }, HitEndTimes = new int[] { 3956, 4583, 5248, 5914 }, FileName = "MCH0207.ani" } },
        { (9, 209, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1797, 2765, 4458 }, HitEndTimes = new int[] { 2074, 2993, 4735 }, FileName = "MCH0209.ani" } },
        { (9, 210, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 1710, 3060, 4340, 5700, 6980 }, HitEndTimes = new int[] { 2070, 3420, 4700, 6130, 7340 }, FileName = "MCH0210.ani" } },
        { (9, 212, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2420, 4210, 6260 }, HitEndTimes = new int[] { 2850, 4650, 6630 }, FileName = "MCH0212.ani" } },
        { (9, 214, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3400 }, HitEndTimes = new int[] { 3900 }, FileName = "MCH0214.ani" } },
        { (9, 216, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2100, 3700 }, HitEndTimes = new int[] { 2450, 4200 }, FileName = "MCH0216.ani" } },
        { (9, 216, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2100, 3700 }, HitEndTimes = new int[] { 2450, 4200 }, FileName = "MCH0216.ani" } },
        { (9, 300, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6200 }, HitEndTimes = new int[] { 6750 }, FileName = "MCH0300.ani" } },
        { (9, 302, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2520, 3320, 4120, 4850, 5700 }, HitEndTimes = new int[] { 2820, 3620, 4430, 5160, 6200 }, FileName = "MCH0302.ani" } },
        { (9, 302, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2520, 3320, 4120, 4850, 5700 }, HitEndTimes = new int[] { 2820, 3620, 4430, 5160, 6200 }, FileName = "MCH0302.ani" } },
        { (9, 305, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 7280, 9300 }, HitEndTimes = new int[] { 7900, 9940 }, FileName = "MCH0305.ani" } },
        { (9, 207, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 3721, 4348, 4974, 5640 }, HitEndTimes = new int[] { 3956, 4583, 5248, 5914 }, FileName = "MCH0207.ani" } },
        { (9, 327, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2795, 3447 }, HitEndTimes = new int[] { 3152, 3785 }, FileName = "MCH0327.ani" } },
        { (9, 342, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 4950, 5100 }, HitEndTimes = new int[] { 5100, 5250 }, FileName = "MCH0342.ani" } },
        { (10, 220, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1700, 3830 }, HitEndTimes = new int[] { 1800, 3930 }, FileName = "MCZ0220.ani" } },
        { (10, 223, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1660, 3060 }, HitEndTimes = new int[] { 1760, 3160 }, FileName = "MCZ0223.ani" } },
        { (10, 211, 0), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 950, 2650, 4250, 7230 }, HitEndTimes = new int[] { 1050, 2750, 4350, 7330 }, FileName = "MCZ0211.ani" } },
        { (10, 158, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 710, 1570, 2550, 3530, 4470 }, HitEndTimes = new int[] { 750, 1600, 2600, 3600, 4550 }, FileName = "MCZ0158.ani" } },
        { (10, 206, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2220, 2690, 3950, 5210 }, HitEndTimes = new int[] { 2270, 2740, 4000, 5260 }, FileName = "AMCZ0206.ani" } },
        { (10, 207, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2220, 2690, 3950, 5210, 6300 }, HitEndTimes = new int[] { 2270, 2740, 4000, 5260, 6350 }, FileName = "AMCZ0207.ani" } },
        { (10, 300, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1750, 4200 }, HitEndTimes = new int[] { 1850, 4300 }, FileName = "AMCZ0300.ani" } },
        { (10, 302, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 710, 1570, 2280, 2920, 6130 }, HitEndTimes = new int[] { 810, 1670, 2380, 3020, 6230 }, FileName = "AMCZ0302.ani" } },
        { (10, 306, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1700, 2850, 4450 }, HitEndTimes = new int[] { 1800, 2950, 4550 }, FileName = "AMCZ0306.ani" } },
        { (10, 308, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2630, 4130, 6170, 7720 }, HitEndTimes = new int[] { 2730, 4230, 6270, 7820 }, FileName = "AMCZ0308.ani" } },
        { (10, 311, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2570, 3230, 3890, 4490 }, HitEndTimes = new int[] { 2620, 3280, 3940, 4540 }, FileName = "AMCZ0311.ani" } },
        { (10, 313, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1250, 4150 }, HitEndTimes = new int[] { 1350, 4250 }, FileName = "AMCZ0313.ani" } },
        { (10, 206, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2220, 2690, 3950, 5210 }, HitEndTimes = new int[] { 2270, 2740, 4000, 5260 }, FileName = "BMCZ0206.ani" } },
        { (10, 208, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 2590, 3000, 5280, 6520, 8440 }, HitEndTimes = new int[] { 2690, 3100, 5300, 6620, 8540 }, FileName = "BMCZ0208.ani" } },
        { (10, 302, 2), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 710, 1570, 2280, 2920, 6130 }, HitEndTimes = new int[] { 810, 1670, 2380, 3020, 6230 }, FileName = "BMCZ0302.ani" } },
        { (10, 306, 2), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 1700, 2850, 4450 }, HitEndTimes = new int[] { 1800, 2950, 4550 }, FileName = "BMCZ0306.ani" } },
        { (10, 308, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2630, 4130, 6170, 7720 }, HitEndTimes = new int[] { 2730, 4230, 6270, 7820 }, FileName = "BMCZ0308.ani" } },
        { (10, 311, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 2570, 3230, 3890, 4490 }, HitEndTimes = new int[] { 2620, 3280, 3940, 4540 }, FileName = "BMCZ0311.ani" } },
        { (10, 313, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1250, 4150 }, HitEndTimes = new int[] { 1350, 4250 }, FileName = "BMCZ0313.ani" } },
        { (10, 315, 1), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 1211, 2186, 3397, 4999 }, HitEndTimes = new int[] { 1282, 2257, 3468, 5070 }, FileName = "AMCZ0315.ani" } },
        { (10, 316, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4759 }, HitEndTimes = new int[] { 4843 }, FileName = "AMCZ0316.ani" } },
        { (10, 318, 1), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3878 }, HitEndTimes = new int[] { 3961 }, FileName = "AMCZ0318.ani" } },
        { (10, 320, 1), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 924, 1945, 2553, 3104, 5147 }, HitEndTimes = new int[] { 1005, 2026, 2634, 3185, 5228 }, FileName = "AMCZ0320.ani" } },
        { (10, 330, 1), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2830, 5550 }, HitEndTimes = new int[] { 3060, 6000 }, FileName = "AMCZ0330.ani" } },
        { (10, 339, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2280, 3350, 5430 }, HitEndTimes = new int[] { 3010, 4460, 6580 }, FileName = "AMCZ0339.ani" } },
        { (10, 342, 1), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 3200, 3850, 4960 }, HitEndTimes = new int[] { 3350, 4000, 5110 }, FileName = "AMCZ0342.ani" } },
        { (10, 319, 2), new AnimationInfo { HitCount = 4, HitTimes = new int[] { 586, 1564, 2466, 3930 }, HitEndTimes = new int[] { 663, 1641, 2543, 4007 }, FileName = "BMCZ0319.ani" } },
        { (10, 326, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1330, 4080 }, HitEndTimes = new int[] { 1430, 4180 }, FileName = "BMCZ0326.ani" } },
        { (10, 331, 2), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3350, 4550 }, HitEndTimes = new int[] { 3540, 5320 }, FileName = "BMCZ0331.ani" } },
        { (10, 341, 2), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4750 }, HitEndTimes = new int[] { 4900 }, FileName = "BMCZ0341.ani" } },
        { (11, 202, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3880 }, HitEndTimes = new int[] { 6040 }, FileName = "MCU0202.ani" } },
        { (11, 403, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 270 }, HitEndTimes = new int[] { 2070 }, FileName = "MCU0403.ani" } },
        { (11, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 130, 7810 }, HitEndTimes = new int[] { 380, 8700 }, FileName = "MCU0212.ani" } },
        { (11, 415, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 2700 }, HitEndTimes = new int[] { 5150 }, FileName = "MCU0415.ani" } },
        { (12, 202, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 2590, 8320 }, HitEndTimes = new int[] { 2900, 8540 }, FileName = "MCO0202.ani" } },
        { (12, 211, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1090, 7680 }, HitEndTimes = new int[] { 1730, 8060 }, FileName = "MCO0211.ani" } },
        { (12, 206, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 1680, 5810 }, HitEndTimes = new int[] { 2390, 7180 }, FileName = "MCO0206.ani" } },
        { (12, 300, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 2510, 3770, 7070 }, HitEndTimes = new int[] { 3240, 4690, 7600 }, FileName = "MCO0300.ani" } },
        { (13, 203, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3080, 5280 }, HitEndTimes = new int[] { 4100, 6500 }, FileName = "MCG0203.ani" } },
        { (13, 204, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 3080, 5280 }, HitEndTimes = new int[] { 4100, 6500 }, FileName = "MCG0204.ani" } },
        { (13, 212, 0), new AnimationInfo { HitCount = 2, HitTimes = new int[] { 510, 8960 }, HitEndTimes = new int[] { 1020, 9340 }, FileName = "MCG0212.ani" } },
        { (13, 214, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 6000 }, HitEndTimes = new int[] { 7000 }, FileName = "MCG0214.ani" } },
        { (13, 217, 0), new AnimationInfo { HitCount = 3, HitTimes = new int[] { 4780, 5010, 5500 }, HitEndTimes = new int[] { 5010, 5310, 5900 }, FileName = "MCG0217.ani" } },
        { (13, 221, 0), new AnimationInfo { HitCount = 5, HitTimes = new int[] { 3500, 4200, 4900, 5600, 6200 }, HitEndTimes = new int[] { 4200, 4900, 5600, 6200, 7000 }, FileName = "MCG0221.ani" } },
        { (13, 301, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 4600 }, HitEndTimes = new int[] { 6500 }, FileName = "MCG0301.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 201, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 1900 }, HitEndTimes = new int[] { 2100 }, FileName = "MCG0201.ani" } },
        { (13, 303, 0), new AnimationInfo { HitCount = 1, HitTimes = new int[] { 3800 }, HitEndTimes = new int[] { 4500 }, FileName = "MCG0303.ani" } }
    };
}

public class AnimationInfo
{
    public int HitCount { get; set; }
    public int[] HitTimes { get; set; }
    public int[] HitEndTimes { get; set; }
    public string FileName { get; set; }
}
